import React, { Dispatch, SetStateAction } from "react";
import { Label } from "@/components/ui/label";
import { Adjustments } from "../EditTool";
import { Slider } from "@/components/ui/slider";

interface BasicEditProps {
    adjustments: Adjustments;
    setAdjustments: Dispatch<SetStateAction<Adjustments>>;
    applyFilter: (key: keyof Adjustments, value: number) => void;
    hasImage: boolean;
}

export function BasicEdit({
    adjustments,
    setAdjustments,
    applyFilter,
    hasImage,
}: BasicEditProps) {
    const handleSliderChange = (key: keyof Adjustments, value: number) => {
        setAdjustments((prev) => ({
            ...prev,
            [key]: value,
        }));
        if (hasImage) {
            applyFilter(key, value);
        }
    };

    return (
        <div className="space-y-4">
            <h3 className="text-xl font-semibold">Basic Edit</h3>
            <div className="space-y-4">
                {(
                    [
                        "temperature",
                        "tint",
                        "brightness",
                        "saturation",
                        "inversion",
                        "grayscale",
                    ] as const
                ).map((key) => {
                    const min = 0;
                    const max =
                        key === "inversion" || key === "grayscale" ? 100 : 200;
                    const currentValue = adjustments[key];

                    return (
                        <div key={key}>
                            <Label className="capitalize">
                                {key}: {currentValue}
                            </Label>
                            <Slider
                                value={[currentValue]}
                                min={min}
                                max={max}
                                step={1}
                                onValueChange={(value) => {
                                    handleSliderChange(key, value[0]);
                                }}
                                className="w-full py-0.5"
                                classNames={{
                                    track: "bg-[#e2e8f0] h-1.5",
                                    range: "bg-[#6938ef] h-1.5",
                                    thumb: "h-3.5 w-3.5 border-[#6938ef] bg-white hover:bg-[#6938ef]/10 shadow",
                                }}
                            />
                        </div>
                    );
                })}
            </div>
        </div>
    );
}
