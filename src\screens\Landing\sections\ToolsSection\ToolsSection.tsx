import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { images } from "@/constants"
import { useState } from "react"

export const ToolsSection = () => {
  const [activeButton, setActiveButton] = useState("Product Design")

  const toolButtons = [
    { label: "Trend Analysis", image: images.trendAnalysisLandingPageImage },
    { label: "Product Design", image: images.productDesignLandingPageImage },
    { label: "Sketch to Render", image: images.sketchToRenderLandingPageImage },
    { label: "Image Mixer", image: images.imageMixer },
    { label: "Precise Edit", image: images.preciseEdit },
    { label: "Infinite Colorways", image: images.infiniteColorways },
    { label: "Technical Drawings", image: images.technicalDrawingsLandingPageImage },
    { label: "Lifestyle Photography", image: images.lifestylePhotographyLandingPageImage },
    { label: "Seamless Prints", image: images.seamlessPrints },
    { label: "Background Generator", image: images.backgroundGeneratorLandingPageImage },
    { label: "3D Avatar to Photorealism", image: images.avatarToPhotorealismLandingPageImage },
    { label: "Design to Techpack", image: images.dashboard }, 
    { label: "Design to CAD", image: images.dashboard }, 
  ]

  const activeImage = toolButtons.find(button => button.label === activeButton)?.image || images.dashboard

  return (
    <section className="w-full px-2 md:px-10 py-8 md:py-12 lg:py-16">
      <div className="container mx-auto px-4 md:px-6">
        <h2 className="mb-4 md:mb-6 lg:mb-8 font-['Plus_Jakarta_Sans',Helvetica] font-bold text-black text-2xl sm:text-3xl md:text-4xl lg:text-5xl tracking-[-0.96px]">
          Game changing AI tools
        </h2>

        <div className="flex flex-nowrap overflow-x-auto pb-2 gap-2 md:flex-wrap md:gap-[8px_10px] mb-6 md:mb-8 lg:mb-10">
          {toolButtons.map((button) => (
            <Button
              key={button.label}
              variant={activeButton === button.label ? "default" : "outline"}
              className={`rounded-xl border border-solid whitespace-nowrap ${
                activeButton === button.label ? "bg-[#6e56cf] text-white" : "bg-white text-slate-950 border-[#d4dae1]"
              } px-2.5 sm:px-3 md:px-4 py-1.5 h-auto text-xs sm:text-sm md:text-base`}
              onClick={() => setActiveButton(button.label)}
            >
              <span className="font-['Plus_Jakarta_Sans',Helvetica] font-medium leading-normal">{button.label}</span>
            </Button>
          ))}
        </div>

        <div className="flex justify-center w-full">
          <Card className="overflow-hidden rounded-2xl md:rounded-3xl p-0 border-0 mb-0 inline-block">
            <CardContent className="p-0 flex">
              <img
                src={activeImage}
                alt={`${activeButton} interface`}
                className="w-full h-auto max-h-[600px] object-contain"
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
