import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useDispatch, useSelector } from "react-redux";
import { 
  startGeneratingImages, 
  setGeneratedImages, 
  updateToolConfiguration,
  setImagesError
} from "@/lib/store/appSlice";
import type { RootState } from "@/types/store.types";
import { ToolConfiguration } from "@/components/toolbar/tool-configuration";
import { ToolLayout } from "@/components/toolbar/tool-layout";
import type { Section, ToolOption } from "@/types/config.types";
import { motion } from "framer-motion";
import { generatePrintsAndPatterns } from "@/lib/api";
import { toast } from "sonner";
import { improvePrompt } from "@/lib/api";
import { useEffect, useState } from "react";
import api from '@/lib/api/api';

// Define the form schema
const formSchema = z.object({
  description: z.string().min(1, "Please describe your design"),
  // ... other fields ...
});

type FormValues = z.infer<typeof formSchema>;

export function PrintsAndPatternsTool() {
  const dispatch = useDispatch();
  const activeTabId = useSelector((state: RootState) => state.app.activeTabId);
  const toolConfiguration = useSelector((state: RootState) => 
    activeTabId ? state.app.toolConfigurations[activeTabId] : null
  );

  // State for reference image description fetching
  const [referenceImageDescriptionLoading, setReferenceImageDescriptionLoading] = useState(false);
  const [shouldFetchReferenceImageDescription, setShouldFetchReferenceImageDescription] = useState(false);

  const {
    setValue,
    formState: { errors },
    trigger,
    watch,
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: toolConfiguration?.description || "",
      // ... other fields ...
    },
  });

  // Watch the description field
  const description = watch("description");

  useEffect(() => {
    const fetchReferenceImageDescription = async () => {
      if (
        activeTabId &&
        toolConfiguration?.referenceImage &&
        !referenceImageDescriptionLoading
      ) {
        // Determine if we *need* to fetch:
        // 1. Explicitly told to fetch (new image upload triggers shouldFetchReferenceImageDescription)
        // 2. Image exists, but its description is missing in the store (e.g., initial load, or previous fetch failed)
        const needsFetching = shouldFetchReferenceImageDescription ||
                              (toolConfiguration.referenceImage && !toolConfiguration.referenceImageDescription);

        if (needsFetching) {
          try {
            setReferenceImageDescriptionLoading(true);
            if (shouldFetchReferenceImageDescription) {
                 setShouldFetchReferenceImageDescription(false); // Consume the explicit trigger
            }

            let imageData = toolConfiguration.referenceImage;
            // Ensure image is in base64 format for the API, if not already
            if (typeof imageData === 'string' && !imageData.startsWith('data:')) {
              imageData = `data:image/png;base64,${imageData}`;
            }

            const describeResponse = await api.post('/describe', {
              base64EncodedImage: imageData
            });

            if (describeResponse.data.status === 'success') {
              dispatch(updateToolConfiguration({
                tabId: activeTabId,
                values: {
                  // Only update referenceImageDescription, assuming updateToolConfiguration merges
                  referenceImageDescription: describeResponse.data.data
                }
              }));
              toast.success('Reference image analyzed successfully');
            } else {
              toast.error('Failed to analyze reference image');
              // Clear potentially stale description from store if analysis fails
              dispatch(updateToolConfiguration({
                tabId: activeTabId,
                values: { referenceImageDescription: undefined }
              }));
            }
          } catch (error) {
            console.error('Failed to analyze reference image:', error);
            toast.error('Failed to analyze reference image');
            if (activeTabId) { // Ensure activeTabId is still valid before dispatching
                dispatch(updateToolConfiguration({
                    tabId: activeTabId,
                    values: { referenceImageDescription: undefined }
                }));
            }
          } finally {
            setReferenceImageDescriptionLoading(false);
          }
        }
      } else if (activeTabId && !toolConfiguration?.referenceImage && toolConfiguration?.referenceImageDescription) {
        // Clean up: if reference image is removed, but its description still exists in store
        dispatch(updateToolConfiguration({
          tabId: activeTabId,
          values: { referenceImageDescription: undefined }
        }));
        // Reset flag if it was somehow set true for a non-existent image
        if (shouldFetchReferenceImageDescription) {
            setShouldFetchReferenceImageDescription(false);
        }
      }
    };

    fetchReferenceImageDescription();
  }, [
    toolConfiguration?.referenceImage,
    toolConfiguration?.referenceImageDescription,
    shouldFetchReferenceImageDescription,
    activeTabId,
    dispatch,
    referenceImageDescriptionLoading
  ]);

  const sections: Section[] = [
    {
      title: "Mode",
      options: [
        {
          type: 'mode',
          key: 'mode',
          props: {
            modes: [
              { 
                label: 'Generate Seamless Print', 
                description: 'Use reference images to inspire the design',
                value: 'seamlessPrint' 
              },
              { 
                label: 'Recolor', 
                description: 'Change colors based on prompt',
                value: 'recolor' 
              },
            ],
          },
          defaultValue: toolConfiguration?.mode || 'seamlessPrint',
        },
      ]
    },
    {
      options: [
        {
          type: 'textarea',
          label: 'Describe your design',
          key: 'description',
          placeholder: 'Add prompt',
          required: true,
          value: description,
          magicWand: {
            show: true,
            onClick: async (currentValue: string) => {
              try {
                const improvedPrompt = await improvePrompt("Prints and Patterns", currentValue);

                dispatch(updateToolConfiguration({
                  tabId: activeTabId,
                  values: { description: improvedPrompt }
                }));

                setValue("description", improvedPrompt, { shouldValidate: true });
                
                toast.success("Prompt improved!");
                return improvedPrompt;
              } catch (error) {
                toast.error("Failed to improve prompt");
                throw error;
              }
            }
          }
        },
      ]
    },
    {
      title: "Reference Image (optional)",
      options: [
        {
          type: 'mode',
          key: 'referenceType',
          props: {
            modes: [
              { 
                label: 'Style Inspiration', 
                description: 'Use up to 3 reference images',
                value: 'styleInspiration' 
              },
              { 
                label: 'Seamless Converter', 
                description: 'Closely follow reference image',
                value: 'seamlessConverter' 
              },
            ],
          },
          defaultValue: toolConfiguration?.referenceType || 'styleInspiration',
        },
        {
          type: 'upload',
          key: 'referenceImage',
          required: false,
        },
        {
          type: 'slider',
          label: 'Weight',
          key: 'weight',
          defaultValue: toolConfiguration?.weight || 50,
          props: { min: 0, max: 100, step: 1 },
        },
        {
          type: 'upload',
          key: 'additionalReferenceImage',
          label: 'Add another reference image',
          required: false,
        }
      ]
    },
    {
      title: "ADVANCED",
      collapsible: true,
      options: [
        {
          type: 'slider-group',
          className: "grid grid-cols-2 gap-4",
          options: [
            {
              type: 'input',
              label: 'Guidance Scale',
              key: 'guidanceScale',
              defaultValue: toolConfiguration?.guidanceScale || 3.0,
            },
            {
              type: 'input',
              label: 'Steps',
              key: 'steps',
              defaultValue: toolConfiguration?.steps || 30,
            },
          ]
        }
      ]
    }
  ];

  const handleGenerate = async (_formValues: Record<string, unknown>) => {
    // _formValues (from ToolConfiguration's internal state) might be slightly different from Redux toolConfiguration
    // if updates are still batching or if not all fields are synced back to RHF.
    // For consistency, especially for fetched data like referenceImageDescription,
    // it's safer to rely on the Redux `toolConfiguration`.

    if (!activeTabId) {
      console.warn('No active tab ID found');
      return;
    }

    const currentConfig = toolConfiguration;
    if (!currentConfig) {
      toast.error("Tool configuration not found. Please try again.");
      return;
    }

    // Validate description (from Redux store, which should be in sync with RHF via handleValueChange/magicWand)
    if (!currentConfig.description || currentConfig.description.trim() === "") {
      toast.error("Please provide a design description.");
      // Manually trigger RHF validation for user feedback on the form field itself
      setValue("description", currentConfig.description, { shouldValidate: true }); 
      return;
    }

    dispatch(startGeneratingImages(activeTabId));
    
    const referenceImage = currentConfig.referenceImage || "";
    
    // Check reference image analysis status if a reference image is provided
    if (referenceImage) {
      if (referenceImageDescriptionLoading) {
        toast.warning("Reference image is still being analyzed. Please wait a moment.");
        return;
      }
    }

    try {
      let description = currentConfig.description;
      const referenceType = currentConfig.referenceType || "styleInspiration";
      // Get the fetched description from Redux store if reference image exists
      const referenceImageDescription = referenceImage ? currentConfig.referenceImageDescription : undefined;
      
      // Modify description based on reference type
      if (referenceType === 'styleInspiration') {
        description = `Style Inspiration: ${description}`;
      } else if (referenceType === 'seamlessConverter') {
        description = `Seamless Converter: ${description}`;
      }
      
      const temperatures = [0.25, 0.5, 0.75, 1];
      const imagePromises = temperatures.map(temp => 
        generatePrintsAndPatterns(
          description, 
          referenceImage,
          referenceImageDescription,
          temp
        )
      );
      
      const generatedUrls = await Promise.all(imagePromises);
      
      dispatch(
        setGeneratedImages({
          tabId: activeTabId,
          images: generatedUrls
        })
      );
      
      toast.success("Generated prints and patterns successfully");
    } catch (error) {
      console.error('Image generation failed:', error);
      dispatch(setImagesError('Failed to generate images. Please try again.'));
      toast.error('Failed to generate images. Please try again.');
    }
  };

  const handleValueChange = (changedValues: Record<string, unknown>) => {
    if (!activeTabId) return;
    
    const updatedValuesForStore = { ...changedValues };

    // Logic for handling referenceImage changes and triggering description fetch
    if (changedValues.hasOwnProperty('referenceImage')) {
        const newReferenceImage = changedValues.referenceImage as string | undefined;
        const oldReferenceImage = toolConfiguration?.referenceImage;

        if (newReferenceImage && newReferenceImage !== oldReferenceImage) {
            // New image uploaded or different image selected
            setShouldFetchReferenceImageDescription(true);
            // Clear any existing description as it's for the old image
            updatedValuesForStore.referenceImageDescription = undefined; 
        } else if (!newReferenceImage && oldReferenceImage) {
            // Reference image was removed (was present, now it's not)
            setShouldFetchReferenceImageDescription(false); // No need to fetch for a non-existent image
            updatedValuesForStore.referenceImageDescription = undefined; // Clear description
        }
        // If newReferenceImage is the same as oldReferenceImage, do nothing regarding description fetching.
    }
    
    // Update Redux store with all changed values
    dispatch(updateToolConfiguration({
      tabId: activeTabId,
      values: updatedValuesForStore
    }));

    // Sync with react-hook-form state for fields managed by it (e.g., 'description')
    Object.entries(changedValues).forEach(([key, value]) => {
        // Check if the key is a field defined in the Zod schema for react-hook-form
        if (key in (formSchema.shape as any)) { 
            setValue(key as keyof FormValues, value as any, { // `as any` for general value type
              shouldValidate: true, // Validate on change for immediate feedback
            });
        }
    });
  };


  const renderCustomField = (option: ToolOption) => {
    if (option.key === 'additionalReferenceImage') {
      return (
        <div className="mt-2">
          <label className="block text-[11px] font-medium text-[#535862] mb-2">
            {option.label}
          </label>
          <div className="relative">
            <input
              type="file"
              accept="image/*"
              className="hidden"
              id={option.key as string}
            />
            <label
              htmlFor={option.key as string}
              className="text-purple-600 text-sm cursor-pointer"
            >
              Add another reference image
            </label>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full"
    >
      <ToolLayout>
        <div className="animate-slideIn">
          <ToolConfiguration
            title="Prints and Patterns"
            type="create"
            sections={sections}
            onGenerate={handleGenerate}
            onChange={handleValueChange}
            initialValues={{ // Prioritize RHF 'description', then spread Redux state
              ...toolConfiguration, // Spread current Redux config first
              description: description, // Override with RHF's watched description
              // Ensure other RHF-managed fields are similarly prioritized if they exist
            } as unknown as Record<string, unknown>}
            renderCustomField={renderCustomField}
          />
        </div>
      </ToolLayout>
    </motion.div>
  );
}