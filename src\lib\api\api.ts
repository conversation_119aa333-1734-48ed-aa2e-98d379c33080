import axios from 'axios';
import { store } from '@/lib/store/store';
import { refreshToken, clearCredentials } from '@/lib/store/authSlice';

const BASE_URL = import.meta.env.VITE_BASE_URL;

// Create axios instance
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const state = store.getState();
    const token = state.auth.accessToken;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => Promise.reject(error)
);

// Queue to store requests that failed due to token expiration
let isRefreshing = false;
let refreshSubscribers = [];

// Function to process queued requests
const processQueue = (error, token = null) => {
  refreshSubscribers.forEach(cb => cb(error, token));
  refreshSubscribers = [];
};

// Add a subscriber to the queue
const addSubscriber = (callback) => {
  refreshSubscribers.push(callback);
};

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    // If the error is not 401 or this request has already been retried, reject it
    if (error.response?.status !== 401 || originalRequest._retry) {
      return Promise.reject(error);
    }
    
    // Mark this request as retried
    originalRequest._retry = true;
    
    // If we're already refreshing the token, queue this request
    if (isRefreshing) {
      return new Promise((resolve, reject) => {
        addSubscriber((err, token) => {
          if (err) {
            return reject(err);
          }
          
          // Update the authorization header
          originalRequest.headers.Authorization = `Bearer ${token}`;
          resolve(api(originalRequest));
        });
      });
    }
    
    isRefreshing = true;
    
    try {
      // Attempt to refresh the token - the cookie will be sent automatically
      const result = await store.dispatch(refreshToken()).unwrap();
      const newToken = result.accessToken;
      
      // Update the authorization header for the original request
      originalRequest.headers.Authorization = `Bearer ${newToken}`;
      
      // Process all the queued requests with the new token
      processQueue(null, newToken);
      
      return api(originalRequest);
    } catch (refreshError) {
      // If refresh fails, clear credentials and redirect to login
      store.dispatch(clearCredentials());
      
      // Process all the queued requests with the error
      processQueue(refreshError);
      
      // Optionally redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
      
      return Promise.reject(refreshError);
    } finally {
      isRefreshing = false;
    }
  }
);

export default api;