import { EditIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

const ProductGrid = ({ products, onProductClick, prefix }) => {
    const renderPriceSection = (price, discountedPrice) => {
        if (!discountedPrice || discountedPrice === price) {
            return (
                <span className="text-xs font-semibold text-gray-900">
                    ₹{price}
                </span>
            );
        }

        const originalPrice = parseFloat(price.replace(/,/g, ''));
        const finalPrice = parseFloat(discountedPrice.replace(/,/g, ''));
        const discount = ((originalPrice - finalPrice) / originalPrice) * 100;

        return (
            <>
                <span className="text-xs font-semibold text-brand">
                    ₹{discountedPrice}
                </span>
                <span className="text-xs text-gray-500 line-through ml-1">
                    ₹{price}
                </span>
                {discount > 0 && (
                    <span className="text-[10px] text-green-600 font-medium ml-1">
                        {Math.round(discount)}% OFF
                    </span>
                )}
            </>
        );
    };

    const handleOpenInEditor = (e, product) => {
        e.stopPropagation();
        console.log("Opening in editor:", product.image_url);
        // Implement editor opening logic here
    };

    return (
        <div className="grid grid-cols-4 gap-6">
            {products.map((product, index) => (
                <div
                    key={`${prefix}-${product.product_id || index}`}
                    className="cursor-pointer group"
                    onClick={() => onProductClick(product)}
                >
                    <div className="flex flex-col">
                        {/* Image Container */}
                        <div className="relative w-full aspect-[3/4] bg-slate-100 rounded-xl overflow-hidden mb-3">
                            <img
                                src={product.image_url}
                                alt={product.description || "Fashion item"}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                            
                            {/* Edit Button */}
                            <Button 
                                onClick={(e) => handleOpenInEditor(e, product)}
                                className="absolute top-3 right-3 bg-white/80 backdrop-blur-sm rounded-full p-1.5 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity"
                                variant="ghost"
                                size="sm"
                            >
                                <EditIcon className="h-4 w-4 text-gray-600" />
                            </Button>
                            
                            {/* Brand name in box */}
                            {product.brand_name && (
                                <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-xs font-medium">
                                    {product.brand_name}
                                </div>
                            )}
                            
                            {/* Source tag - if present */}
                            {product.source && !product.brand_name && (
                                <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-xs font-medium">
                                    {product.source}
                                </div>
                            )}
                        </div>

                        {/* Product Info */}
                        <div className="space-y-1">
                            <p className="text-sm text-gray-900 line-clamp-2">
                                {product.product_name}
                            </p>
                            <div className="flex items-center gap-1 mt-1">
                                {renderPriceSection(product.price, product.discounted_price)}
                            </div>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
};

export default ProductGrid;