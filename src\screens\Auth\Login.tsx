import { LoginForm } from "@/components/LoginForm";
import AuthLayout from "./AuthLayout";
import { useSelector } from "react-redux";
import { Navigate } from "react-router-dom";
import { RootState } from "@/types/store.types";

export default function Login() {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <AuthLayout>
      <LoginForm />
    </AuthLayout>
  );
}
