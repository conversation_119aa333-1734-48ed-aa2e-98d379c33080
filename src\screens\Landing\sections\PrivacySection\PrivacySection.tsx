import { Card, CardContent } from "@/components/ui/card"
import { images } from "@/constants"

const identityCards = [
  {
    icon: images.contentOwnership,
    title: "Content ownership",
    description: "You retain full ownership and IP of any designs created using Rapid Runway",
  },
  {
    icon: images.dataPrivacy,
    title: "Data privacy",
    description: "We never share, cross-pollinate, sell or use the assets you upload or generate",
  },
  {
    icon: images.peaceOfMind,
    title: "Complete peace of mind",
    description: "Enterprise packages come with its own private cloud hosting server. A private island for your data",
  },
]

export const PrivacySection = () => {
  return (
    <section className="w-full px-6 md:px-8 py-16 md:py-24 bg-[#f1eef9]">
      <div className="container mx-auto max-w-[1440px]">
        <h2 className="mb-8 md:mb-10 font-bold text-3xl md:text-5xl tracking-[-0.96px] font-['Plus_Jakarta_Sans',Helvetica]">
          Your designs remains yours, fully private
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {identityCards.map((card, index) => (
            <Card key={index} className="rounded-[20px] overflow-hidden">
              <CardContent className="p-6 flex flex-col gap-4">
                <div className="w-[50px] h-[50px] bg-[#f1eef9] rounded-lg flex items-center justify-center">
                  <img className="w-6 h-6" alt={card.title} src={card.icon || "/placeholder.svg"} />
                </div>

                <div className="flex flex-col gap-2">
                  <h3 className="font-['Plus_Jakarta_Sans',Helvetica] font-bold text-[#000000b2] text-xl md:text-2xl leading-9">
                    {card.title}
                  </h3>
                  <p className="text-stone-500 text-base md:text-lg font-['Inter',Helvetica] font-normal leading-[26px]">
                    {card.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
