import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { store, persistor } from '@/lib/store/store'
import { default as Landing } from './screens/Landing/Landing'
import { Dashboard } from './screens/Dashboard/Dashboard'
import { Toaster } from "@/components/ui/sonner"
import Login from './screens/Auth/Login'
import ProtectedRoute from './screens/Auth/ProtectedRoute'
import { useEffect } from 'react'

function AppRoutes() {
  // This effect helps ensure the API interceptors are set up after hydration
  useEffect(() => {
    // This is intentionally empty, but it ensures the API module is imported
    // and interceptors are registered after the component mounts
  }, []);

  return (
    <Routes>
      <Route path="/" element={<Landing />} />
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } 
      />
      <Route path="/login" element={<Login />} />
      {/* <Route path='/register' element={<Register />} /> */}
      {/* Catch all route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}

function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <BrowserRouter>
          <AppRoutes />
          <Toaster />
        </BrowserRouter>
      </PersistGate>
    </Provider>
  )
}

export default App