import React, { useState } from "react";
import { Button } from "@/components/ui/button";

interface PatternEditProps {
    applyPattern: (file: File) => void;
    resetPattern: () => void;
}

export function PatternEdit({ applyPattern }: PatternEditProps) {
    const [patternFile, setPatternFile] = useState<File | null>(null);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files?.[0]) {
            setPatternFile(e.target.files[0]);
        }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (e.dataTransfer.files?.[0]) {
            setPatternFile(e.dataTransfer.files[0]);
        }
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
    };

    const handleApplyClick = () => {
        if (patternFile) {
            applyPattern(patternFile);
        }
    };

    return (
        <div className="space-y-4">
            <h3 className="text-xl font-semibold">Add Pattern</h3>

            <div
                className="cursor-pointer flex items-center gap-2 px-4 py-2 border rounded-lg hover:bg-gray-50"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() =>
                    document.getElementById("patternFileInput")?.click()
                }
            >
                <input
                    id="patternFileInput"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                />
                {patternFile ? (
                    <p className="text-gray-700">{patternFile.name}</p>
                ) : (
                    <p className="text-gray-500">Select or drop file</p>
                )}
            </div>

            <div className="flex space-x-4">
                <Button
                    className="w-full"
                    onClick={handleApplyClick}
                    disabled={!patternFile}
                >
                    Apply
                </Button>
            </div>
        </div>
    );
}
