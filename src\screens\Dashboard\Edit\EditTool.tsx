import React, { useState, useEffect } from "react";
import * as fabric from 'fabric';
import { useImageEditor } from "./ImageEditor/useImageEditor";
import { CanvasArea } from "./ImageEditor/CanvasArea";
import { Undo, Redo, RefreshCcw, Trash2, Crop, Check, X, Sciss<PERSON>, Layers } from "lucide-react";
import { motion } from "framer-motion";
import { Header } from "./Header/Header";
import { ModeTabs } from "./ModeTabs/ModeTabs";

import { MagicEdit } from "./Modes/MagicEdit";
import { ColorEdit } from "./Modes/ColorEdit";
import { PatternEdit } from "./Modes/PatternEdit";
import { MaterialEdit } from "./Modes/MaterialEdit";
import { BasicEdit } from "./Modes/BasicEdit";
import { Button } from "@/components/ui/button";
import { useSAM } from "@/hooks/useSAM";
import { CutoutsPanel } from "../SidebarTools/CutoutsPanel";
import { toast } from "sonner";

export type Mode = "magic" | "color" | "pattern" | "material" | "basic";

export type MagicSubMode = "smart" | "brush";

export type UseCase = "insert" | "erase";

export type Adjustments = {
    temperature: number;
    tint: number;
    brightness: number;
    saturation: number;
    inversion: number;
    grayscale: number;
};

interface CutoutItem {
    id: string;
    imageDataUrl: string;
    name?: string;
}

const defaultAdjustments: Adjustments = {
    temperature: 100,
    tint: 100,
    brightness: 100,
    saturation: 100,
    inversion: 0,
    grayscale: 0,
};

type SidebarTab = "edit-tools" | "cutouts";

export function EditTool() {
    const [selectedMode, setSelectedMode] = useState<Mode>("magic");
    const [magicSubMode, setMagicSubMode] = useState<MagicSubMode>("smart");
    const [activeSidebarTab, setActiveSidebarTab] = useState<SidebarTab>("edit-tools");

    const [prompt, setPrompt] = useState("");
    const [color, setColor] = useState<string>("#ffffff");
    const [adjustments, setAdjustments] =
        useState<Adjustments>(defaultAdjustments);

    const sam = useSAM();

    const imageEditor = useImageEditor(selectedMode, magicSubMode);
    const {
        canvasRef,
        canvasContainerRef,
        samMaskCanvasRef,
        originalImage,
        isLoading: isImageEditorLoading,
        handleFileUpload,
        canvas,
        undo: editorUndo,
        redo: editorRedo,
        canUndo: editorCanUndo,
        canRedo: editorCanRedo,
        generateMaskAndSend,
        generateRemoveBackground,
        hasImage,
        resetImage: resetImageFromEditor,
        removeImage,
        cancelCrop,
        applyFillColor,
        applyPattern,
        applyMaterial,
        applyFilter,
        cursorCanvasRef,
        testLog,
    } = imageEditor;

    const [isCropping, setIsCropping] = useState<boolean>(false);
    const [cutouts, setCutouts] = useState<CutoutItem[]>([]);

    const isLoading = isImageEditorLoading || sam.samState.isLoading;

    const localHandleConfirmCrop = () => {
        if(imageEditor.confirmCrop) imageEditor.confirmCrop();
        setIsCropping(false);
    };

    // Helper function to get base data URL from image element
    const getBaseDataURL = (imgElement: HTMLImageElement): string => {
        const tempCanvas = new fabric.Canvas(null);
        tempCanvas.setWidth(imgElement.naturalWidth);
        tempCanvas.setHeight(imgElement.naturalHeight);
        const fabricImg = new fabric.FabricImage(imgElement, { left: 0, top: 0 });
        tempCanvas.add(fabricImg);
        const dataUrl = tempCanvas.toDataURL({ format: 'png', quality: 1, multiplier: 1 });
        tempCanvas.dispose();
        return dataUrl;
    };

    // Function to manually generate SAM embedding
    const handleGenerateEmbedding = async () => {
        if (!originalImage) {
            toast.error("No image available for Smart Select processing.");
            return;
        }

        const fabricImageElement = originalImage.getElement() as HTMLImageElement;
        if (!fabricImageElement) {
            toast.error("Could not process image for Smart Select.");
            return;
        }

        try {
            const dataUrl = getBaseDataURL(fabricImageElement);
            await sam.generateEmbedding(dataUrl);
            toast.success("Image processed for Smart Select!");
        } catch (error) {
            console.error("Error generating SAM embedding:", error);
            toast.error("Failed to process image for Smart Select.");
        }
    };

    useEffect(() => {
        if (!canvas || selectedMode !== "magic" || magicSubMode !== "smart" || !originalImage || !sam.samState.tensor) {
            if (canvas) {
                canvas.off("mouse:move");
                canvas.off("mouse:down");
            }
            const canvasContainerElement = canvasContainerRef.current;
            if (canvasContainerElement && (canvasContainerElement as any)._mouseOutHandler) {
                 canvasContainerElement.removeEventListener('mouseout', (canvasContainerElement as any)._mouseOutHandler);
                 delete (canvasContainerElement as any)._mouseOutHandler;
            }
            return;
        }

        const handleCanvasMouseMove = (options: any) => {
            if (!originalImage || !sam.samState.modelScale || !options.e) return;
            const pointer = canvas.getPointer(options.e as MouseEvent);

            const imgLeft = originalImage.left || 0;
            const imgTop = originalImage.top || 0;
            const imgScaleX = originalImage.scaleX || 1;
            const imgScaleY = originalImage.scaleY || 1;

            if (
                pointer.x >= imgLeft &&
                pointer.x <= imgLeft + originalImage.getScaledWidth() &&
                pointer.y >= imgTop &&
                pointer.y <= imgTop + originalImage.getScaledHeight()
            ) {
                const x = (pointer.x - imgLeft) / imgScaleX;
                const y = (pointer.y - imgTop) / imgScaleY;
                sam.handleMouseMoveOverImage(x, y);
            } else {
                sam.clearHoverMask();
            }
        };

        const handleCanvasMouseDown = (options: any) => {
            if (!originalImage || !sam.samState.modelScale || !options.e) return;
            const pointer = canvas.getPointer(options.e as MouseEvent);

            const imgLeft = originalImage.left || 0;
            const imgTop = originalImage.top || 0;
            const imgScaleX = originalImage.scaleX || 1;
            const imgScaleY = originalImage.scaleY || 1;

            if (
                pointer.x >= imgLeft &&
                pointer.x <= imgLeft + originalImage.getScaledWidth() &&
                pointer.y >= imgTop &&
                pointer.y <= imgTop + originalImage.getScaledHeight()
            ) {
                const x = (pointer.x - imgLeft) / imgScaleX;
                const y = (pointer.y - imgTop) / imgScaleY;
                sam.handleClick(x, y);
            }
        };

        const handleCanvasContainerMouseOut = () => {
            sam.clearHoverMask();
        };

        canvas.on("mouse:move", handleCanvasMouseMove);
        canvas.on("mouse:down", handleCanvasMouseDown);
        const canvasContainerElement = canvasContainerRef.current;
        if (canvasContainerElement) {
            (canvasContainerElement as any)._mouseOutHandler = handleCanvasContainerMouseOut;
            canvasContainerElement.addEventListener('mouseout', handleCanvasContainerMouseOut);
        }

        return () => {
            canvas.off("mouse:move", handleCanvasMouseMove);
            canvas.off("mouse:down", handleCanvasMouseDown);
            if (canvasContainerElement && (canvasContainerElement as any)._mouseOutHandler) {
                canvasContainerElement.removeEventListener('mouseout', (canvasContainerElement as any)._mouseOutHandler);
                 delete (canvasContainerElement as any)._mouseOutHandler;
            }
            sam.clearHoverMask();
        };
    }, [canvas, originalImage, selectedMode, magicSubMode, sam, canvasContainerRef]);

    const handleResetAll = () => {
        resetImageFromEditor();
        setPrompt("");
        setColor("#ffffff");
        setAdjustments(defaultAdjustments);
        sam.reset();
        setCutouts([]);
    };

    const handleFinalizeCutout = () => {
        if (!originalImage || !sam.samState.maskImg) {
            toast.error("No selection available to create a cutout.");
            return;
        }
        const fabricImageElement = originalImage.getElement() as HTMLImageElement;
        if (fabricImageElement && fabricImageElement.naturalWidth > 0) {
            const cutoutResult = sam.generateCutout(fabricImageElement);
            if (cutoutResult) {
                const newCutout: CutoutItem = {
                    id: `cutout-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`,
                    imageDataUrl: cutoutResult.segmentedCutout,
                    name: `Cutout ${cutouts.length + 1}`
                };
                setCutouts(prev => [...prev, newCutout]);
                toast.success("Cutout created and added to 'Cut Outs' panel!");
                sam.reset();
            } else {
                toast.error("Failed to generate cutout.");
            }
        } else {
            toast.error("Cannot generate cutout from the current image type or image not loaded.");
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3 }}
            className="flex h-full p-4"
        >
            <div className="w-[360px] flex-shrink-0 h-[calc(100vh-48px)] flex flex-col bg-white border-r border-gray-200">
                <div className="p-3 border-b border-[#e2e8f0]">
                    <Header />
                    <div className="mt-4 flex border-b border-gray-200">
                        <button
                            onClick={() => setActiveSidebarTab("edit-tools")}
                            className={`flex-1 py-2 px-3 text-sm font-medium text-center -mb-px border-b-2 transition-colors duration-150
                                ${activeSidebarTab === "edit-tools" ? "border-purple-600 text-purple-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
                        >
                            Edit Tools
                        </button>
                        <button
                            onClick={() => setActiveSidebarTab("cutouts")}
                            className={`flex-1 py-2 px-3 text-sm font-medium text-center -mb-px border-b-2 transition-colors duration-150
                                ${activeSidebarTab === "cutouts" ? "border-purple-600 text-purple-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`}
                        >
                            Cut Outs ({cutouts.length})
                        </button>
                    </div>
                </div>

                <div className="flex-1 overflow-y-auto custom-scrollbar">
                  {activeSidebarTab === "edit-tools" && (
                    <div className="p-3 space-y-6">
                      <ModeTabs
                          selectedMode={selectedMode}
                          setSelectedMode={(mode) => {
                              setSelectedMode(mode);
                              sam.reset();
                          }}
                      />
                      {selectedMode === "magic" && (
                          <MagicEdit
                              imageEditor={imageEditor}
                              prompt={prompt}
                              setPrompt={setPrompt}
                              generateMaskAndSend={generateMaskAndSend}
                              generateRemoveBackground={generateRemoveBackground}
                              magicSubMode={magicSubMode}
                              setMagicSubMode={(subMode) => {
                                  setMagicSubMode(subMode);
                                  sam.reset();
                              }}
                              testLog={testLog}
                              samState={sam.samState}
                              isSmartModeActive={magicSubMode === 'smart'}
                              onGenerateEmbedding={handleGenerateEmbedding}
                              hasImage={hasImage}
                          />
                      )}
                      {selectedMode === "color" && (
                        <ColorEdit
                            color={color}
                            setColor={setColor}
                            applyFillColor={applyFillColor}
                            resetCanvas={resetImageFromEditor}
                        />
                      )}
                      {selectedMode === "pattern" && (
                        <PatternEdit
                            applyPattern={applyPattern}
                            resetPattern={resetImageFromEditor}
                        />
                      )}
                      {selectedMode === "material" && (
                        <MaterialEdit
                            applyMaterial={applyMaterial}
                            resetMaterial={resetImageFromEditor}
                        />
                      )}
                      {selectedMode === "basic" && (
                        <BasicEdit
                            adjustments={adjustments}
                            setAdjustments={setAdjustments}
                            applyFilter={applyFilter}
                            hasImage={hasImage}
                        />
                      )}
                    </div>
                  )}
                  {activeSidebarTab === "cutouts" && (
                    <CutoutsPanel
                      cutouts={cutouts}
                      onDeleteCutout={(id) => setCutouts(prev => prev.filter(c => c.id !== id))}
                    />
                  )}
                </div>
            </div>

            {/* Canvas Area */}
            <div className="flex-1 flex flex-col m-3 rounded-2xl bg-white border border-[#e2e8f0] overflow-hidden">
                <div className="flex-1 relative bg-gray-50 flex flex-col">
                    <div className="flex-grow relative">
                        <CanvasArea
                            canvasRef={canvasRef}
                            canvasContainerRef={canvasContainerRef}
                            samMaskCanvasRef={samMaskCanvasRef}
                            originalImage={originalImage}
                            isLoading={isLoading}
                            onFileUpload={handleFileUpload}
                            cursorCanvasRef={cursorCanvasRef}
                            sam={sam}
                            selectedMode={selectedMode}
                            magicSubMode={magicSubMode}
                        />
                    </div>

                    {/* Bottom toolbar */}
                    <div className="h-16 bg-white border-t border-gray-200 flex items-center justify-center p-2 z-10">
                        <div className="flex items-center space-x-2">
                            {isCropping ? (
                                <>
                                    <Button variant="ghost" size="icon" onClick={localHandleConfirmCrop} aria-label="Confirm Crop">
                                        <Check className="h-5 w-5" />
                                    </Button>
                                    <Button variant="ghost" size="icon" onClick={cancelCrop} aria-label="Cancel Crop">
                                        <X className="h-5 w-5" />
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <Button variant="ghost" size="icon" onClick={editorUndo} disabled={!editorCanUndo} aria-label="Undo Edit">
                                        <Undo className="h-5 w-5" />
                                    </Button>
                                    <Button variant="ghost" size="icon" onClick={editorRedo} disabled={!editorCanRedo} aria-label="Redo Edit">
                                        <Redo className="h-5 w-5" />
                                    </Button>

                                    {selectedMode === 'magic' && magicSubMode === 'smart' && hasImage && sam.samState.tensor && (
                                        <>
                                            <div className="h-6 border-l border-gray-300 mx-1"></div>
                                            <Button variant="ghost" size="icon" onClick={sam.undo} disabled={!sam.canUndo} aria-label="Undo Smart Selection">
                                                <Undo className="h-5 w-5 text-blue-600" />
                                            </Button>
                                            <Button variant="ghost" size="icon" onClick={sam.redo} disabled={!sam.canRedo} aria-label="Redo Smart Selection">
                                                <Redo className="h-5 w-5 text-blue-600" />
                                            </Button>
                                            <Button variant="ghost" size="icon" onClick={sam.reset} disabled={sam.samState.clicks.length === 0 && !sam.samState.maskImg} aria-label="Reset Smart Selection">
                                                <RefreshCcw className="h-5 w-5 text-orange-600" />
                                            </Button>
                                             <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={sam.toggleClickType}
                                                aria-label={sam.samState.isPositiveClick ? "Switch to Negative Point" : "Switch to Positive Point"}
                                                title={sam.samState.isPositiveClick ? "Negative Point (Subtract)" : "Positive Point (Add)"}
                                            >
                                                {sam.samState.isPositiveClick ?
                                                    <span className="text-xl font-bold text-green-500">+</span> :
                                                    <span className="text-2xl font-bold text-red-500 ">-</span>
                                                }
                                            </Button>
                                            <Button variant="ghost" size="icon" onClick={sam.invertMask} title="Invert Mask (Selection)" disabled={!sam.samState.maskImg && !sam.samState.hoverMaskImg }>
                                                <Layers className="h-5 w-5" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={handleFinalizeCutout}
                                                disabled={!sam.samState.maskImg || sam.samState.clicks.length === 0}
                                                className="ml-2"
                                                aria-label="Finalize Cutout"
                                            >
                                                <Scissors className="h-4 w-4 mr-2" />
                                                Create Cutout
                                            </Button>
                                        </>
                                    )}
                                     <div className="h-6 border-l border-gray-300 mx-1"></div>
                                     <Button variant="ghost" size="icon" onClick={() => {
                                         setIsCropping(true);
                                         if (imageEditor.startCrop) imageEditor.startCrop();
                                         }}
                                         disabled={!hasImage || isCropping}
                                         aria-label="Crop Image"
                                     >
                                         <Crop className="h-5 w-5" />
                                    </Button>
                                    <Button variant="ghost" size="icon" onClick={handleResetAll} disabled={!hasImage} aria-label="Reset All Changes">
                                        <RefreshCcw className="h-5 w-5" />
                                    </Button>
                                    <Button variant="ghost" size="icon" onClick={removeImage} disabled={!hasImage} aria-label="Remove Image">
                                        <Trash2 className="h-5 w-5 text-red-500" />
                                    </Button>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    );
}
