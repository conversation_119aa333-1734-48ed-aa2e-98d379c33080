import { Card, CardContent } from "@/components/ui/card"
import { images } from "@/constants"
export const CategoriesSection = () => {
  const categories = [
    {
      name: "Clothing",
      image: images.clothingImage,
    },
    {
      name: "Runway",
      image: images.runwayImage,
    },
    {
      name: "Jewelry",
      image: images.jewelryImage,
    },
    {
      name: "Footwear",
      image: images.footwearImage,
    },
    {
      name: "Sneakers",
      image: images.sneakersImage,
    },
    {
      name: "Sketch",
      image: images.sketchImage,
    },
    {
      name: "Bags",
      image: images.bagsImage,
    },
    {
      name: "Watch<PERSON>",
      image: images.watchesImage,
    },
  ]

  return (
    <section className="relative w-full px-6 md:px-8 py-16 md:py-24">
      <div className="container mx-auto max-w-[1440px]">
        <h2 className="font-bold text-3xl md:text-5xl tracking-[-0.96px] [font-family:'Plus_Jakarta_Sans',Helvetica] text-black mb-6 md:mb-10">
          All categories
        </h2>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 md:gap-8">
          {categories.map((category, index) => (
            <div key={index} className="flex flex-col items-center justify-center gap-3">
              <Card className="w-3/4 aspect-square rounded-[20px] overflow-hidden border-none shadow-sm p-0">
                <CardContent className="p-0 h-full relative flex items-center justify-center">
                  <img
                    className="w-full h-full object-cover"
                    alt={`${category.name} category`}
                    src={category.image || "/placeholder.svg"}
                  />
                </CardContent>
              </Card>
              <span className="font-bold text-lg md:text-xl text-center leading-[30px] [font-family:'Plus_Jakarta_Sans',Helvetica] text-[#000000b2]">
                {category.name}
              </span>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
