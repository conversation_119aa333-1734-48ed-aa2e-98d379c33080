"use client"

import type React from "react"

import { useState } from "react"
import WaitlistModal from "../WaitlistModal/WaitlistModal"
import { Input } from "@/components/ui/input"

export const WaitlistSection = () => {
  const [email, setEmail] = useState("")

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value)
  }

  return (
    <section className="w-full px-2 md:px-10 py-12 md:py-24 flex flex-col items-center justify-center">
      <div className="container mx-auto px-4 md:px-6 max-w-[1440px] text-center">
        <h2 className="font-bold text-3xl md:text-5xl tracking-[-0.96px] font-['Plus_Jakarta_Sans',Helvetica] mb-6 md:mb-8">
          Join our waitlist
        </h2>

        <div className="flex flex-col md:flex-row justify-center items-center gap-4 max-w-md mx-auto">
          <Input
            type="email"
            placeholder="<EMAIL>"
            className="w-full md:flex-grow"
            value={email}
            onChange={handleEmailChange}
          />
          <WaitlistModal prefilledEmail={email} />
        </div>
      </div>
    </section>
  )
}
