import { TrendingUpIcon } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const TrendingSection = ({ trendingItems, handleSearch }) => {
    return (
        <>
            <div className="flex items-center gap-2">
                <TrendingUpIcon className="w-4 h-4" />
                <span className="font-text-sm-medium text-slate-950">
                    Trending
                </span>
            </div>

            <div className="flex flex-col gap-3.5">
                <div className="flex items-center gap-3">
                    {trendingItems.slice(0, 3).map((item) => (
                        <Card
                            key={item.id}
                            className="flex items-center gap-4 pr-4 py-0 pl-0 border border-solid border-slate-200 shadow-[0px_1px_2px_#0a0d120d] rounded-md cursor-pointer"
                            onClick={() => handleSearch(item.text)}
                        >
                            <CardContent className="p-0">
                                <div className="flex items-center">
                                    <div className="relative w-[69px] h-[52px] overflow-hidden">
                                        <img
                                            className="absolute object-cover"
                                            alt={item.text}
                                            src={item.image}
                                            style={{
                                                width: "80px",
                                                height: "106px",
                                                top: "-5px",
                                                left: "-0.5px",
                                            }}
                                        />
                                    </div>
                                    <span className="font-text-sm-regular text-slate-500 ml-4">
                                        {item.text}
                                    </span>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                <div className="flex items-center gap-3">
                    {trendingItems.slice(3, 6).map((item) => (
                        <Card
                            key={item.id}
                            className="flex items-center gap-4 pr-4 py-0 pl-0 border border-solid border-slate-200 shadow-[0px_1px_2px_#0a0d120d] rounded-md cursor-pointer"
                            onClick={() => handleSearch(item.text)}
                        >
                            <CardContent className="p-0">
                                <div className="flex items-center">
                                    <div className="relative w-[69px] h-[52px] overflow-hidden">
                                        <img
                                            className="absolute object-cover"
                                            alt={item.text}
                                            src={item.image}
                                            style={{
                                                width: "80px",
                                                height: "106px",
                                                top: "-5px",
                                                left: "-0.5px",
                                            }}
                                        />
                                    </div>
                                    <span className="font-text-sm-regular text-slate-500 ml-4">
                                        {item.text}
                                    </span>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        </>
    );
};

export default TrendingSection;