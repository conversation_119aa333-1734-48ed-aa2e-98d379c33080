import { ReactNode } from "react";
import { images } from "@/constants";

interface AuthLayoutProps {
  children: ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="grid min-h-screen overflow-hidden lg:grid-cols-[40%_60%]">
      <div className="relative hidden bg-muted lg:block">
        <img
          src={images.loginImage}
          alt="Image"
          className="absolute h-full w-full object-cover bg-[#1C0C45]"
        />
      </div>
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="w-fit inline-flex items-center gap-2 px-3 py-1.5 rounded-md">
          <img
            className="w-[24px] h-8"
            alt="Logo"
            src={images.logo}
          />
          <div className="font-semibold text-black text-xl tracking-[-0.36px] font-['Plus_Jakarta_Sans',Helvetica]">
            Rapid Runway
          </div>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}