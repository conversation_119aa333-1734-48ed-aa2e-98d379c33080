// src/utils/posthogClient.js
import posthog from 'posthog-js';

export const initPostHog = () => {
  console.log("post hog initialised");
  posthog.init(import.meta.env.VITE_POSTHOG_API_KEY, {
    api_host: 'https://app.posthog.com', // or your self-hosted URL
    autocapture: true,
    capture_pageview: true,
    disable_session_recording: false,
    // Enable persistence using localStorage
    persistence: 'localStorage',
  });
};

export const identifyUser = (userId, properties = {}) => {
  posthog.identify(userId, properties);
};

export const captureEvent = (event, properties = {}) => {
  console.log("capture event called");
  posthog.capture(event, properties);
};

export const resetPostHog = () => {
  posthog.reset(); // logout case
};
