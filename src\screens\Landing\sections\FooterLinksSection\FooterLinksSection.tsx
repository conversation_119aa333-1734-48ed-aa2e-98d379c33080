import { images } from "@/constants"

export const FooterLinksSection = () => {
  // const navLinks = [
  //   { title: "Solutions", hasDropdown: true },
  //   { title: "About Us", hasDropdown: false },
  //   { title: "Pricing", hasDropdown: false },
  //   { title: "Security", hasDropdown: false },
  // ];

  return (
    <footer className="relative w-full bg-[#31196f] py-12 md:py-24">
      <div className="container mx-auto px-4 md:px-6 max-w-[1440px] flex flex-col md:flex-row items-center md:items-start gap-8 md:gap-12">
        <div className="flex flex-col items-center md:items-start space-y-6">
          {/* Logo area */}
          <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-md [background:linear-gradient(135deg,rgba(108,62,223,1)_0%,rgba(92,0,146,1)_100%)]">
            <img className="w-[24px] h-8" alt="Logo" src={images.logo || "/placeholder.svg"} />
            <div className="font-semibold text-white text-lg tracking-[-0.36px] font-['Plus_Jakarta_Sans',Helvetica]">
              Rapid Runway
            </div>
          </div>

          {/* Social media icons */}
          <div className="flex items-center gap-3">
            <img className="w-[23px] h-[23px]" alt="LinkedIn" src={images.linkedin || "/placeholder.svg"} />
            <img className="w-[23px] h-[23px]" alt="Facebook" src={images.facebook || "/placeholder.svg"} />
            <img className="w-[23px] h-[23px]" alt="Instagram" src={images.instagram || "/placeholder.svg"} />
            <img className="w-[23px] h-[23px]" alt="Twitter" src={images.twitter || "/placeholder.svg"} />
          </div>
        </div>

        {/* Navigation links */}
        {/* commented out for now */}
        {/* <div className="flex flex-col space-y-4 md:flex-1">
          {navLinks.map((link, index) => (
            <div key={index} className="flex items-center gap-0.5">
              <div className="font-['Plus_Jakarta_Sans',Helvetica] font-medium text-white text-xs">
                {link.title}
              </div>
            </div>
          ))}
        </div> */}

        {/* Coming Soon text */}
        <div className="md:ml-auto font-['Plus_Jakarta_Sans',Helvetica] font-bold text-white text-3xl md:text-5xl tracking-[-0.96px]">
          Coming Soon...
        </div>
      </div>
    </footer>
  )
}
