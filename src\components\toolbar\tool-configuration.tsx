import { useState } from "react"
import { useSelector, useDispatch } from "react-redux"
import { ChevronDown, Upload, CheckCircle, X, WandIcon } from "lucide-react"
import { toast } from "sonner"
import ReactCrop, { type C<PERSON>, centerCrop, makeAspectCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';

import type { RootState } from "@/types/store.types"
import type { ToolOption } from "@/types/config.types"
import { Slider } from "@/components/ui/slider"
import { ToolHeader } from "./tool-header"
import { clearGeneratedImages } from "@/lib/store/appSlice"
import { cn } from "@/lib/utils"
import { APP_CONFIG } from "@/lib/config/app-config"
import { captureEvent } from "@/utils/postHogClient";

interface Section {
  title?: string
  collapsible?: boolean
  options: ToolOption[]
}

interface ToolConfigurationProps {
  title: string
  sections: Section[]
  onGenerate: (values: Record<string, unknown>) => void
  customActions?: React.ReactNode
  description?: string
  productDescription?: string
  onChange?: (values: Record<string, unknown>) => void
  initialValues?: Record<string, unknown>
  layout?: "default" | "grid"
  type?: 'create' | 'edit' | 'mix'
  renderCustomField?: (option: ToolOption) => React.ReactNode
}

export function ToolConfiguration({ 
  title, 
  sections,
  onGenerate,
  customActions,
  description,
  onChange,
  initialValues = {},
  type = 'create',
  renderCustomField,
}: ToolConfigurationProps) {
  const activeTabId = useSelector((state: RootState) => state.app.activeTabId)
  const isLoading = useSelector((state: RootState) => 
    activeTabId ? state.app.isLoading.images[activeTabId] || false : false
  )
  const activeApp = useSelector((state: RootState) => state.app.activeApp)
  const appConfig = APP_CONFIG[activeApp]
  const [values, setValues] = useState<Record<string, unknown>>(initialValues)
  const [collapsedSections, setCollapsedSections] = useState<Record<string, boolean>>({
    "Advanced": true  // true means collapsed
  })
  const dispatch = useDispatch()
  const [tempFile, setTempFile] = useState<{file: File, key: string, preview: string} | null>(null);
  const [showCropModal, setShowCropModal] = useState(false);
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 90,
    height: 90,
    x: 5,
    y: 5
  });
  const [imgRef, setImgRef] = useState<HTMLImageElement | null>(null);
  const [magicWandLoading, setMagicWandLoading] = useState<Record<string, boolean>>({});

  const handleValueChange = (key: string, value: unknown) => {
    const newValues = { ...values, [key]: value };
    setValues(newValues);
    onChange?.(newValues);
  }

  const handleGenerate = () => {
    captureEvent("generate_button_clicked", {"mini_app_name": title});
    onGenerate?.(values);
  }

  const handleReset = () => {
    setValues({})
    if (activeTabId) {
      dispatch(clearGeneratedImages(activeTabId))
    }
  }

  const toggleSection = (sectionTitle: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [sectionTitle]: !prev[sectionTitle]
    }))
  }

  const handleFileUpload = async (file: File, key: string) => {
    try {
      console.log('Starting file upload:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        key
      });

      // Store the file temporarily for cropping
      setTempFile({
        file,
        key,
        preview: URL.createObjectURL(file)
      });
      setShowCropModal(true);

    } catch (error) {
      console.error('Error initiating file read:', error);
      toast.error('Failed to upload image', {
        description: 'Please try again',
      });
    }
  };

  const handleCropConfirm = async () => {
    if (!tempFile || !imgRef) return;
    
    try {
      const canvas = document.createElement('canvas');
      const scaleX = imgRef.naturalWidth / imgRef.width;
      const scaleY = imgRef.naturalHeight / imgRef.height;
      
      canvas.width = crop.width * scaleX;
      canvas.height = crop.height * scaleY;
      
      const ctx = canvas.getContext('2d');
      if (!ctx) return;
      
      ctx.drawImage(
        imgRef,
        crop.x * scaleX,
        crop.y * scaleY,
        crop.width * scaleX,
        crop.height * scaleY,
        0,
        0,
        crop.width * scaleX,
        crop.height * scaleY
      );
      
      canvas.toBlob((blob) => {
        if (!blob || !tempFile) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
          const base64 = e.target?.result as string;
          handleValueChange(tempFile.key, base64);
          toast.success('Image cropped and uploaded successfully', {
            description: tempFile.file.name,
          });
        };
        reader.readAsDataURL(blob);
        
        setShowCropModal(false);
        setTempFile(null);
      }, 'image/jpeg');
      
    } catch (error) {
      console.error('Error cropping image:', error);
      toast.error('Failed to crop image', {
        description: 'Please try again',
      });
    }
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    setImgRef(e.currentTarget);
    
    const initialCrop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        16 / 9, // You can set this to undefined for free-form cropping
        width,
        height
      ),
      width,
      height
    );
    
    setCrop(initialCrop);
  };

  const renderOption = (option: ToolOption) => {
    if (renderCustomField) {
      const customRendered = renderCustomField(option);
      if (customRendered) return customRendered;
    }
    
    switch (option.type) {
      case 'textarea':
        return (
          <div className="space-y-1">
            <label className="block text-[11px] font-medium text-[#535862]">
              {option.label}
              {option.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <div className="relative">
              <textarea
                className={cn(
                  "w-full h-20 p-1.5 text-xs border border-[#e2e8f0] rounded-md resize-none focus:outline-none focus:ring-1 focus:ring-[#6938ef] focus:border-transparent",
                  option.error && "border-red-500 focus:ring-red-500"
                )}
                placeholder={option.placeholder}
                value={option.value !== undefined ? option.value : (values[option.key] as string | number) || ''}
                onChange={(e) => handleValueChange(option.key, e.target.value)}
              />
              {option.magicWand?.show && (
                <div className="absolute bottom-1.5 right-1.5 flex items-center">
                  <div className="relative group">
                    <div className="absolute z-20 invisible opacity-0 right-full mr-2 top-1/2 -translate-y-1/2 whitespace-nowrap bg-[#f8fafc] text-[#535862] text-xs font-medium rounded-md py-1.5 px-3 border border-[#e2e8f0] shadow-sm group-hover:visible group-hover:opacity-100 transition-all duration-200 pointer-events-none">
                      <div className="flex items-center">
                        Enhance your prompt with AI assistance
                      </div>
                    </div>
                    <button
                      type="button"
                      className={cn(
                        "p-1.5 rounded-full transition-all",
                        !(values[option.key])
                          ? "text-gray-300 cursor-not-allowed"
                          : "text-[#6938ef] hover:bg-[#f4f3ff] cursor-pointer"
                      )}
                      disabled={!(values[option.key]) || magicWandLoading[option.key]}
                      onClick={async () => {
                        if (option.magicWand?.onClick && values[option.key]) {
                          setMagicWandLoading(prev => ({ ...prev, [option.key]: true }));
                          try {
                            const result = await option.magicWand.onClick(values[option.key] as string);
                            handleValueChange(option.key, result);
                          } finally {
                            setMagicWandLoading(prev => ({ ...prev, [option.key]: false }));
                          }
                        }
                      }}
                    >
                      {magicWandLoading[option.key] ? (
                        <div className="animate-spin">
                          <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </div>
                      ) : (
                        <WandIcon className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
            {option.error && (
              <p className="text-red-500 text-xs mt-1">{option.error}</p>
            )}
          </div>
        );

      case 'upload':
        return (
          <div className="space-y-1">
            <label className="block text-[11px] font-medium text-[#535862]">
              {option.label}
              {option.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  if (option.disabled) {
                    e.preventDefault();
                    return;
                  }
                  const file = e.target.files?.[0];
                  if (file) {
                    handleFileUpload(file, option.key as string);
                  } else {
                    console.warn('No file selected');
                  }
                }}
                className="hidden"
                id={option.key}
                key={values[option.key] ? 'hasValue' : 'empty'} // Add this line to force input reset
              />
              {values[option.key] ? (
                <div className="flex items-center gap-2 px-4 py-2 border rounded-lg bg-green-50 border-green-200">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <div className="w-8 h-8 rounded overflow-hidden border border-green-200 group">
                        <img 
                          src={values[option.key] as string} 
                          alt="Uploaded preview" 
                          className="w-full h-full object-contain"
                        />
                        <div className="absolute hidden group-hover:block z-10 top-full left-0 mt-2 p-2 bg-white rounded-lg shadow-lg border border-gray-200">
                          <div className="w-[200px] h-[200px]">
                            <img 
                              src={values[option.key] as string} 
                              alt="Preview" 
                              className="w-full h-full object-contain"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <CheckCircle size={16} className="text-green-500" />
                    <span className="text-sm text-green-700">Image uploaded successfully</span>
                  </div>
                  <button 
                    onClick={() => {
                      handleValueChange(option.key, '');
                      const fileInput = document.getElementById(option.key) as HTMLInputElement;
                      if (fileInput) {
                        fileInput.value = '';
                      }
                    }}
                    className="ml-auto hover:text-red-500"
                  >
                    <X size={16} />
                  </button>
                </div>
              ) : (
                <label
                  htmlFor={option.key}
                  className={cn(
                    "cursor-pointer flex items-center gap-2 px-4 py-2 border rounded-lg hover:bg-gray-50", 
                    option.disabled && "opacity-50 cursor-not-allowed pointer-events-none"
                  )}
                >
                  <Upload size={16} />
                  <span className="text-md">Upload Image</span>
                </label>
              )}
            </div>
            {option.error && (
              <p className="text-red-500 text-xs mt-1">{option.error}</p>
            )}
          </div>
        );

      case 'preset':  
        return (
          <div className="mb-6">
            <label className="block text-[11px] font-medium text-[#535862] mb-2">{option.label}</label>
            <div className="flex">
              {option.props?.presets?.map((preset, index) => (
                <div 
                  key={index} 
                  className={cn(
                    "flex-1 border-2 p-1 flex flex-col items-center justify-center gap-2 cursor-pointer hover:bg-[#f8fafc]",
                    index === 0 && "rounded-l-lg border-r-0",
                    index === option.props.presets.length - 1 && "rounded-r-lg",
                    index !== 0 && index !== option.props.presets.length - 1 && "border-r-0"
                  )}
                >
                  {preset.icon}
                  <span className="text-xs">{preset.label}</span>
                  {preset.description && (
                    <span className="text-[10px] text-gray-500">{preset.description}</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )
      case 'mode':  
        return (
          <div className="mb-6">
            <label className="block text-[11px] font-medium text-[#535862] mb-2">{option.label}</label>
            <div className="flex">
              {option.props?.modes?.map((mode, index) => (
                <div 
                  key={index} 
                  onClick={() => handleValueChange(option.key, mode.value)}
                  className={cn(
                    "flex-1 border-1 p-1 flex flex-col items-center justify-center gap-2 cursor-pointer hover:bg-[#f8fafc]",
                    values[option.key] === mode.value 
                      ? "border-[#6938ef] bg-[#f4f3ff]" 
                      : "border-[#e2e8f0]",
                    index === 0 && "rounded-l-lg border-r-1",
                    index === option.props.modes.length - 1 && "rounded-r-lg",
                    index !== 0 && index !== option.props.modes.length - 1 && "border-r-1"
                  )}
                >
                  {mode.icon}
                  <span className="text-xs font-medium">{mode.label}</span>
                  {mode.description && (
                    <span className="text-[10px] text-gray-500">{mode.description}</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )

      case 'slider':
        return (
          <div className="space-y-1">
            <div className="flex justify-between items-center">
              <label className="text-[11px] font-medium text-[#535862]">{option.label}</label>
              <span className="text-[11px] text-[#535862]">
                {(values[option.key] as number || option.defaultValue)}
              </span>
            </div>
            <Slider
              value={[Number(values[option.key] || option.defaultValue)]}
              min={option.props?.min}
              max={option.props?.max}
              step={option.props?.step}
              onValueChange={(value) => handleValueChange(option.key, value[0])}
              className="w-full py-0.5"
              classNames={{
                track: "bg-[#e2e8f0] h-0.5",
                range: "bg-[#6938ef] h-0.5",
                thumb: "h-2.5 w-2.5 border-[#6938ef] bg-white hover:bg-[#6938ef]/10"
              }}
            />
          </div>
        )

      case 'slider-group':
        return (
          <div className={cn("mb-6", option.className)}>
            {option.options.map((sliderOption, index) => (
              <div key={index} className={sliderOption.className}>
                <label className={cn(
                  "block text-[11px] font-medium text-[#535862] mb-2",
                  sliderOption.sliderClassNames?.label
                )}>
                  {sliderOption.label}
                  <span className={cn(
                    "float-right",
                    sliderOption.sliderClassNames?.value
                  )}>
                    {(values[sliderOption.key] as number || sliderOption.defaultValue)}
                  </span>
                </label>
                <Slider
                  value={[Number(values[sliderOption.key] || sliderOption.defaultValue)]}
                  min={sliderOption.props?.min}
                  max={sliderOption.props?.max}
                  step={sliderOption.props?.step}
                  onValueChange={(value) => handleValueChange(sliderOption.key, value[0])}
                  className={cn("w-full", sliderOption.sliderClassNames?.container)}
                  classNames={{
                    track: cn("bg-[#e2e8f0]", sliderOption.sliderClassNames?.track),
                    range: cn("bg-[#6938ef]", sliderOption.sliderClassNames?.range),
                    thumb: cn("border-[#6938ef] bg-white hover:bg-[#6938ef]/10", 
                      sliderOption.sliderClassNames?.thumb)
                  }}
                />
              </div>
            ))}
          </div>
        )

      case 'input':
        return (
          <div className="space-y-1">
            <label className="block text-[11px] font-medium text-[#535862]">{option.label}</label>
            <input
              type="number"
              className="w-full h-7 px-1.5 text-xs border border-[#e2e8f0] rounded-md focus:outline-none focus:ring-1 focus:ring-[#6938ef] focus:border-transparent"
              value={(values[option.key] as string | number) || option.defaultValue || ''}
              onChange={(e) => {
                const value = Math.max(0, parseInt(e.target.value) || 0);
                handleValueChange(option.key, value.toString());
              }}
              onKeyDown={(e) => {
                if (e.key === '.' || e.key === '-' || e.key === 'e') {
                  e.preventDefault();
                }
              }}
            />
          </div>
        )
    }
  }

  return (
    <div className="h-[calc(100vh-48px)] flex flex-col bg-white">
      <div className="flex-1 overflow-auto custom-scrollbar">
        <div className="p-3 ">
          <ToolHeader title={title} icon={appConfig?.icon} type={type}/>
          
          {description && (
            <p className="text-[11px] text-[#535862] leading-tight">
              {description}
            </p>
          )}

          {sections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="mb-8">
              {section.title && (
                <div 
                  className={`flex items-center gap-1.5 text-[#535862] text-xs font-medium mb-2 ${
                    section.collapsible ? 'cursor-pointer hover:text-[#6938ef]' : ''
                  }`}
                  onClick={() => section.collapsible && toggleSection(section.title!)}
                >
                  <span>{section.title}</span>
                  {section.collapsible && (
                    <ChevronDown 
                      size={14} 
                      className={`transition-transform ${
                        !collapsedSections[section.title!] ? "rotate-180" : ""
                      }`} 
                    />
                  )}
                </div>
              )}

              {(!section.collapsible || !collapsedSections[section.title!]) && (
                <div className={cn(
                  "space-y-2",
                  section.title === "Advanced" && "border border-[#e2e8f0] rounded-lg p-4"
                )}> 
                  {section.options.map((option, optionIndex) => (
                    <div key={optionIndex} className="animate-fadeIn">
                      {renderOption(option)}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="sticky bottom-0 p-3 bg-white/80 backdrop-blur-sm  border-t border-[#e2e8f0] mr-3">
        {customActions || (
          <div className="flex gap-2">
            <button
              className="flex-1 h-10 text-sm bg-[#f4f3ff] text-[#6938ef] font-medium rounded-md hover:bg-[#ebe9fe] transition-colors cursor-pointer"
              onClick={handleReset}
              disabled={isLoading}
            >
              Reset
            </button>
            <button
              className="flex-1 h-10 text-sm bg-[#6938ef] text-white font-medium rounded-md hover:bg-[#6938ef]/90 transition-colors disabled:opacity-70 cursor-pointer"
              onClick={handleGenerate}
              disabled={isLoading}
            >
              {isLoading ? "Generating..." : "Generate"}
            </button>
          </div>
        )}
      </div>

      {showCropModal && tempFile && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full overflow-hidden">
            <div className="border-b border-gray-200 px-6 py-4 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">Crop Image</h3>
              <button 
                onClick={() => {
                  setShowCropModal(false);
                  setTempFile(null);
                }}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-6 bg-gray-50">
              <div className="mb-4 bg-white rounded-md overflow-hidden shadow-sm flex items-center justify-center">
                <ReactCrop
                  crop={crop}
                  onChange={(c) => setCrop(c)}
                  aspect={undefined}
                  circularCrop={false}
                  keepSelection={true}
                  className="max-h-[60vh] flex items-center justify-center"
                >
                  <img 
                    src={tempFile.preview} 
                    alt="Crop preview" 
                    className="max-h-[60vh] object-contain"
                    onLoad={onImageLoad}
                  />
                </ReactCrop>
              </div>
              
              <div className="text-xs text-gray-500 mb-4 text-center">
                Drag the corners or edges to adjust the crop area
              </div>
            </div>
            
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between gap-3">
              <button 
                onClick={() => {
                  const reader = new FileReader();
                  reader.onload = (e) => {
                    const base64 = e.target?.result as string;
                    handleValueChange(tempFile.key, base64);
                    toast.success('Original image uploaded successfully', {
                      description: tempFile.file.name,
                    });
                  };
                  reader.readAsDataURL(tempFile.file);
                  
                  setShowCropModal(false);
                  setTempFile(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Use Original
              </button>
              
              <div className="flex gap-3">
                <button 
                  onClick={() => {
                    setShowCropModal(false);
                    setTempFile(null);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button 
                  onClick={handleCropConfirm}
                  className="px-4 py-2 bg-[#6938ef] text-white rounded-md text-sm font-medium hover:bg-[#6938ef]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#6938ef]"
                >
                  Apply Crop
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}