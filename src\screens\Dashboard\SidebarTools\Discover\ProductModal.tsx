import { 
    <PERSON><PERSON>, 
    <PERSON><PERSON><PERSON>ontent, 
    <PERSON><PERSON><PERSON><PERSON>er, 
    <PERSON>alogTitle, 
    DialogClose 
} from "@/components/ui/dialog";
import { XIcon, Loader2Icon, EditIcon, GlobeIcon } from "lucide-react";
import { Button } from "@/components/ui/button";

export function ProductModal({ 
    open, 
    onOpenChange, 
    selectedProduct, 
    similarProducts, 
    loadingSimilar, 
    onProductClick 
}) {
    if (!selectedProduct) return null;

    const renderPriceSection = (price, discountedPrice) => {
        if (!discountedPrice || discountedPrice === price) {
            return (
                <span className="text-sm font-semibold text-gray-900">
                    ₹{price}
                </span>
            );
        }

        const originalPrice = parseFloat(price.replace(/,/g, ''));
        const finalPrice = parseFloat(discountedPrice.replace(/,/g, ''));
        const discount = ((originalPrice - finalPrice) / originalPrice) * 100;

        return (
            <>
                <span className="text-sm font-semibold text-brand">
                    ₹{discountedPrice}
                </span>
                <span className="text-sm text-gray-500 line-through ml-2">
                    ₹{price}
                </span>
                {discount > 0 && (
                    <span className="text-xs text-green-600 font-medium ml-2">
                        {Math.round(discount)}% OFF
                    </span>
                )}
            </>
        );
    };

    const handleOpenInEditor = () => {
        // Logic to open the image in your AI editor
        console.log("Opening in editor:", selectedProduct.image_url);
        // Implement your editor opening logic here
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-5xl max-h-[90vh] overflow-y-auto">
                <DialogHeader className="border-b pb-4">
                    <DialogTitle className="text-lg">Product Details</DialogTitle>
                    <DialogClose className="absolute right-4 top-4">
                        <XIcon className="h-4 w-4" />
                        <span className="sr-only">Close</span>
                    </DialogClose>
                </DialogHeader>

                <div className="grid grid-cols-1 md:grid-cols-12 gap-6 pt-4">
                    {/* Product Image & Details - Left column */}
                    <div className="md:col-span-5 flex flex-col">
                        {/* Product Image */}
                        <div className="relative aspect-square bg-slate-100 rounded-xl overflow-hidden mb-4">
                            <img
                                src={selectedProduct.image_url}
                                alt={selectedProduct.description || "Product image"}
                                className="w-full h-full object-contain"
                            />
                            
                            {/* Brand name in box - top left of image */}
                            {selectedProduct.brand_name && (
                                <div className="absolute bottom-3 left-3 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-xs font-medium">
                                    {selectedProduct.brand_name}
                                </div>
                            )}
                            
                            {/* Open in Editor button - top right of image */}
                            <Button 
                                onClick={handleOpenInEditor}
                                className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full p-2 hover:bg-white"
                                variant="ghost"
                                size="sm"
                            >
                                <EditIcon className="h-4 w-4 text-gray-700" />
                                <span className="ml-1 text-xs">Edit</span>
                            </Button>
                        </div>
                        
                        {/* Product Info under image */}
                        <div className="flex flex-col">
                            <h2 className="text-xl font-semibold mb-2">{selectedProduct.product_name}</h2>
                            
                            <div className="flex items-center mb-4">
                                {renderPriceSection(selectedProduct.price, selectedProduct.discounted_price)}
                            </div>
                            
                            {selectedProduct.description && (
                                <p className="text-gray-700 mb-4">{selectedProduct.description}</p>
                            )}
                            
                            {selectedProduct.details && (
                                <div className="border-t pt-3">
                                    <h3 className="font-medium mb-2">Product Details</h3>
                                    <p className="text-sm text-gray-600">{selectedProduct.details}</p>
                                </div>
                            )}
                            
                            {/* Visit Product Button */}
                            {selectedProduct.product_link && (
                                <Button 
                                    className="mt-4 w-full flex items-center justify-center gap-2"
                                    onClick={() => window.open(selectedProduct.product_link, '_blank')}
                                    variant="outline"
                                >
                                    <GlobeIcon className="h-4 w-4" />
                                    Visit on {selectedProduct.source || 'Retailer'}
                                </Button>
                            )}
                        </div>
                    </div>

                    {/* Similar Products - Right column */}
                    <div className="md:col-span-7">
                        <h3 className="text-lg font-medium mb-4">Similar Products</h3>
                        
                        {loadingSimilar ? (
                            <div className="flex justify-center items-center py-12">
                                <Loader2Icon className="h-8 w-8 animate-spin text-gray-400" />
                                <span className="ml-3 text-gray-500">Loading similar products...</span>
                            </div>
                        ) : similarProducts && similarProducts.length > 0 ? (
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                {similarProducts.map((product, index) => (
                                    <div 
                                        key={`similar-${product.product_id || index}`}
                                        className="cursor-pointer group"
                                        onClick={() => {
                                            onProductClick(product);
                                        }}
                                    >
                                        <div className="flex flex-col">
                                            {/* Image Container */}
                                            <div className="relative w-full aspect-[3/4] bg-slate-100 rounded-lg overflow-hidden mb-2">
                                                <img
                                                    src={product.image_url}
                                                    alt={product.description || "Fashion item"}
                                                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                                                />
                                                
                                                {/* Brand name in box */}
                                                {product.brand_name && (
                                                    <div className="absolute bottom-2 left-2 bg-white/90 backdrop-blur-sm rounded-full px-2 py-0.5 text-xs font-medium">
                                                        {product.brand_name}
                                                    </div>
                                                )}
                                            </div>

                                            {/* Product Info */}
                                            <div>
                                                <p className="text-sm text-gray-900 line-clamp-1">
                                                    {product.product_name}
                                                </p>
                                                <div className="flex items-center gap-1 mt-1">
                                                    {renderPriceSection(product.price, product.discounted_price)}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                No similar products found
                            </div>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}