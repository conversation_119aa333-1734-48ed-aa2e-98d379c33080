import { useState } from "react";
import { cn } from "@/lib/utils";
import { MoreVertical, ThumbsUp, ThumbsDown } from "lucide-react";
import Lottie from "lottie-react";
import lottie from "@/constants/lottie";
import { useSelector } from "react-redux";
import type { RootState } from "@/types/store.types";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {ImageDetailModal} from "./ImageDetailModal"

interface ImageGridProps {
    images: string[];
    className?: string;
    isLoading?: boolean;
    loadingCount?: number;
}

const handleDownload = async (imageUrl: string) => {
    try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `generated-image-${Date.now()}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('Download failed:', error);
    }
};

export function ImageGrid({
    images = [],
    className,
    isLoading = false,
    loadingCount = 4,
}: ImageGridProps) {
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    
    // Get the active tab's configuration for prompt info
    const activeTabId = useSelector((state: RootState) => state.app.activeTabId);
    const toolConfiguration = useSelector((state: RootState) => 
        activeTabId ? state.app.toolConfigurations[activeTabId] : null
    );

    const handleImageClick = (src: string) => {
        setSelectedImage(src);
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setIsModalOpen(false);
    };

    const handleLike = (e: React.MouseEvent, src: string) => {
        e.stopPropagation();
        console.log('Liked image:', src);
        // TODO: Add PostHog analytics tracking here
        // posthog.capture('image_liked', { imageUrl: src });
    };
    
    const handleDislike = (e: React.MouseEvent, src: string) => {
        e.stopPropagation();
        console.log('Disliked image:', src);
        // TODO: Add PostHog analytics tracking here
        // posthog.capture('image_disliked', { imageUrl: src });
    };

    // Ensure images is an array
    const imageArray = Array.isArray(images) ? images : [];

    if (isLoading) {
        return (
            <div className={cn(
                "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 w-full h-full p-5",
                className
            )}>
                {Array(loadingCount).fill(null).map((_, index) => (
                    <div
                        key={index}
                        className="relative w-full aspect-[3/4] bg-gray-50 rounded-lg overflow-hidden"
                    >
                        <div className="absolute inset-0 flex items-center justify-center">
                            <Lottie
                                animationData={lottie.lottieLoader}
                                loop={true}
                                className="absolute w-[150%] h-[150%] left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
                                style={{ margin: 0, padding: 0 }}
                            />
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    // If images is empty array (after ensuring it's an array), show a placeholder
    if (imageArray.length === 0) {
        return (
            <div className={cn(
                "w-full h-full p-5 flex items-center justify-center",
                className
            )}>
                <div className="text-gray-400 text-center">
                    <p>No images available</p>
                </div>
            </div>
        );
    }

    const formattedDate = new Date().toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
    });

    return (
        <>
            <div className={cn(
                "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 w-full h-full p-5",
                className
            )}>
                {imageArray.map((src, index) => (
                    <div
                        key={index}
                        className="group relative w-full aspect-[3/4] bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all cursor-pointer"
                        onClick={() => handleImageClick(src)}
                    >
                        <div className="absolute inset-0">
                            <img
                                src={src}
                                alt=""
                                className="w-full h-full object-cover"
                                loading="lazy"
                            />
                        </div>
                        {/* Like/Dislike buttons */}
                        <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity z-10 flex gap-2">
                            <button 
                                className="p-1.5 bg-white/90 rounded-full hover:bg-green-100 transition-colors"
                                onClick={(e) => handleLike(e, src)}
                                title="Like this image"
                            >
                                <ThumbsUp size={16} className="text-green-600" />
                            </button>
                            <button 
                                className="p-1.5 bg-white/90 rounded-full hover:bg-red-100 transition-colors"
                                onClick={(e) => handleDislike(e, src)}
                                title="Dislike this image"
                            >
                                <ThumbsDown size={16} className="text-red-600" />
                            </button>
                        </div>
                        {/* Existing dropdown menu */}
                        <div 
                            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                            onClick={(e) => e.stopPropagation()} 
                        >
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <button className="p-1 bg-white/90 rounded-md hover:bg-white transition-colors">
                                        <MoreVertical size={16} />
                                    </button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem className="font-bold border-b" onClick={(e) => {
                                        e.stopPropagation();
                                        // Handle open in editor action
                                    }}>
                                        Open in Editor
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={(e) => {
                                        e.stopPropagation();
                                        handleDownload(src);
                                    }}>
                                        Download
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={(e) => {
                                        e.stopPropagation();
                                        // Handle share action
                                    }}>
                                        Share
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                ))}
            </div>

            {/* Detail Modal */}
            <ImageDetailModal
                isOpen={isModalOpen}
                onClose={closeModal}
                imageUrl={selectedImage || ''}
                prompt={toolConfiguration?.description || toolConfiguration?.productDescription}
                generatedDate={formattedDate}
            />
        </>
    );
}
