import type React from "react"
import { useDispatch, useSelector } from "react-redux"
import { v4 as uuidv4 } from "uuid"
import { Compass, Bookmark, FileText,
  MessageSquare, Sparkles, Library, History,
} from "lucide-react"

import type { RootState } from "@/types/store.types"
import type { AppType } from "@/types/app.types"
import { setHovering } from "@/lib/store/sidebarSlice"
import { 
  setActiveApp, 
  addTab, 
  setActiveTab,
  updateToolConfiguration 
} from "@/lib/store/appSlice"
import { cn } from "@/lib/utils"
import { APP_CONFIG } from "@/lib/config/app-config"
import { Separator } from "@/components/ui/separator"
import { images } from "@/constants"
import { captureEvent } from "@/utils/postHogClient";

export function Sidebar() {
  const dispatch = useDispatch()
  const { activeApp, tabs } = useSelector((state: RootState) => state.app)
  const { isHovering } = useSelector((state: RootState) => state.sidebar)

  const handleMouseEnter = () => {
    dispatch(setHovering(true))
  }

  const handleMouseLeave = () => {
    dispatch(setHovering(false))
  }

  const handleAppClick = (app: AppType) => {
    const appConfig = APP_CONFIG[app]

    if (appConfig) {
      if (appConfig.showTabs) {
        // Check if there's already a tab for this app type, and activate it if so
        const existingTab = tabs.find(tab => tab.type === app)
        if (existingTab) {
          // If tab exists, just activate it
          dispatch(setActiveTab(existingTab.id))
          dispatch(setActiveApp(app))
        } else {
          // Create new tab with unique ID
          const newTabId = uuidv4()
          
          // Initialize tool configuration for the new tab
          dispatch(
            updateToolConfiguration({
              tabId: newTabId,
              values: {
                description: "",
                referenceImage: "",
                guidanceScale: 7.5, // default value
                steps: 30, // default value
                seed: "",
                presets: []
              }
            })
          )

          // Create the new tab
          dispatch(
            addTab({
              id: newTabId,
              type: app,
              title: appConfig.title,
            })
          )
        }
      } else {
        // For apps that don't support tabs
        dispatch(setActiveApp(app))
      }
    }
  }

  const isExpanded = isHovering

  return (
    <div
      className={cn(
        "border-r border-[#e2e8f0] bg-[#F8FAFC] flex flex-col",
        "transition-all duration-300 ease-in-out",
        isExpanded ? "w-[280px]" : "w-[68px]",
        "h-screen md:h-screen md:flex",
        "z-20",
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="h-12 border-b border-[#e2e8f0] flex items-center px-6">
        <div className={cn(
          "flex items-center gap-3",
          !isExpanded && "justify-center w-full" 
        )}>
            <img 
              src={images.logo1} 
              alt="" 
              className={cn(
                "w-5 h-5",
                !isExpanded && "w-6 h-6"
              )}
            />
          {isExpanded && <span className="text-[#414651] font-semibold text-lg">Rapid Runway</span>}
        </div>
      </div>

      <div className="flex-1 overflow-auto">
        <div className="py-2 space-y-1">
          {/* <NavItem 
            icon={<Home size={20} />} 
            label="Home" 
            isExpanded={isExpanded}
            onClick={() => handleAppClick("home")}
            active={activeApp === "home"}
          /> */}

          <div className="px-5 py-1">
            <Separator />
          </div>

          {isExpanded && (
            <div className="px-5 mb-1">
              <h3 className="text-xs font-medium text-[#717680] tracking-wider">TRENDS</h3>
            </div>
          )}

          <NavItem 
            icon={<Compass size={20} />} 
            label="Discover" 
            isExpanded={isExpanded}
            onClick={() => {
              captureEvent("discover_toolbar_clicked", {});
              handleAppClick("discover");
            }}
            active={activeApp === "discover"}
          />
          <NavItem 
            icon={<Bookmark size={20} />} 
            label="Favourites" 
            isExpanded={isExpanded}
            onClick={() => handleAppClick("favourites")}
            active={activeApp === "favourites"}
          />
          <NavItem 
            icon={<FileText size={20} />} 
            label="Curations & Reports" 
            isExpanded={isExpanded}
            onClick={() => handleAppClick("curations")}
            active={activeApp === "curations"}
          />
          <NavItem
            icon={<MessageSquare size={20} />}
            label="TrendGPT"
            isExpanded={isExpanded}
            onClick={() => {
              captureEvent("trendgpt_toolbar_clicked", {});
              handleAppClick("trendgpt");
            }}
            active={activeApp === "trendgpt"}
          />

          <div className="px-5 py-1">
            <Separator />
          </div>

          {isExpanded && (
            <div className="px-5 mb-1">
              <h3 className="text-xs font-medium text-[#717680] tracking-wider">AI STUDIO</h3>
            </div>
          )}
          <NavItem
            icon={<Sparkles size={20} />}
            label="AI Tools"
            isExpanded={isExpanded}
            active={activeApp === "dashboard"}
            onClick={() => {
              captureEvent("aitools_toolbar_clicked", {});
              handleAppClick("dashboard");
            }}
          />
          <NavItem 
            icon={<Library size={20} />} 
            label="My Library" 
            isExpanded={isExpanded}
            onClick={() => handleAppClick("library")}
            active={activeApp === "library"}
          />
          <NavItem 
            icon={<History size={20} />} 
            label="History" 
            isExpanded={isExpanded}
            onClick={() => handleAppClick("history")}
            active={activeApp === "history"}
          />

          <div className="px-5 py-1">
            <Separator />
          </div>
{/* 
          {isExpanded && (
            <div className="px-5 mb-1">
              <h3 className="text-xs font-medium text-[#717680] tracking-wider">MANUFACTURE</h3>
            </div>
          )}
          <NavItem icon={<Search size={20} />} label="Search Existing Catalog" isExpanded={isExpanded} />
          <NavItem icon={<ShoppingBag size={20} />} label="My Orders" isExpanded={isExpanded} />

          <div className="px-5 py-1">
            <Separator />
          </div>

          {isExpanded && (
            <div className="px-5 mb-1">
              <h3 className="text-xs font-medium text-[#717680] tracking-wider">EXPERIMENT</h3>
            </div>
          )}
          <NavItem icon={<Store size={20} />} label="Storefront" isExpanded={isExpanded} />
          <NavItem icon={<MessageSquare size={20} />} label="Calling and Feedback" isExpanded={isExpanded} /> */}
        </div>
      </div>

    </div>
  )
}

interface NavItemProps {
  icon: React.ReactNode
  label: string
  isExpanded: boolean
  active?: boolean
  onClick?: () => void
}

function NavItem({ icon, label, isExpanded, active = false, onClick }: NavItemProps) {
  return (
    <div
      className={cn(
        "flex items-center h-9 cursor-pointer",
        isExpanded ? "px-5" : "px-3 justify-center",
        "text-[#535862] hover:bg-[#f8fafc]",
      )}
      onClick={onClick}
    >
      {isExpanded ? (

        <div className={cn(
          "flex items-center gap-3 px-3 py-1.5 rounded-lg w-full",
          active ? "bg-[#f4f3ff]" : ""
        )}>
          <div className="w-5 h-5 flex items-center justify-center">
            <div className={cn(
              active ? "text-brand" : ""
            )}>
              {icon}
            </div>
          </div>
          <span className={cn(
            active ? "font-medium text-brand" : ""
          )}>
            {label}
          </span>
        </div>
      ) : (
        
        <div className={cn(
          "w-7 h-7 flex items-center justify-center rounded-lg",
          active ? "bg-[#f4f3ff] text-brand" : ""
        )}>
          {icon}
        </div>
      )}
    </div>
  )
}


















