import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { images } from "@/constants"

export const HeroSection = () => {
  const [activeIndex, setActiveIndex] = useState(0)

  const sentences = ["Get inspired & follow trends.", "Design faster than ever before.", "Get to stores in weeks."]

  const cardImages = [
    {
      id: 1,
      src: images.fashionDesignerImage1,
      alt: "Fashion designer journey image 1",
    },
    {
      id: 2,
      src: images.fashionDesignerImage3,
      alt: "Fashion designer journey image 2",
    },
    {
      id: 3,
      src: images.fashionDesignerImage2,
      alt: "Fashion designer journey image 3",
    },
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % cardImages.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [cardImages.length])

  // Calculate positions for each card with the active one in the center
  const getCardStyles = (index) => {
    let position = index - activeIndex

    // Ensure we get a circular effect by calculating the shortest path around the carousel
    if (position >= cardImages.length / 2) position -= cardImages.length
    if (position < -cardImages.length / 2) position += cardImages.length

    // Calculate styling based on position and screen size
    const xOffset = position * (window.innerWidth < 768 ? 120 : 350) // Smaller offset for mobile
    const zIndex = 10 - Math.abs(position * 2)
    const scale = position === 0 ? 1 : window.innerWidth < 768 ? 0.85 : 0.75 // Larger scale on mobile
    const opacity = position === 0 ? 1 : window.innerWidth < 768 ? 0.7 : 0.6 // Higher opacity on mobile

    return {
      transform: `translateX(${xOffset}px) scale(${scale})`,
      zIndex,
      opacity,
      filter: position === 0 ? "none" : "brightness(0.7)",
    }
  }

  // Add window resize listener to update carousel
  useEffect(() => {
    const handleResize = () => {
      // Force a re-render when window is resized
      setActiveIndex((prevIndex) => prevIndex)
    }

    window.addEventListener("resize", handleResize)
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  return (
    <section className="relative w-full py-16 md:py-24 bg-[#f1eef9] overflow-hidden">
      <div className="container mx-auto px-4 md:px-6 max-w-[1440px]">
        <h2 className="text-3xl md:text-5xl font-bold mb-6 md:mb-10 font-['Plus_Jakarta_Sans',Helvetica] text-black tracking-[-0.96px] text-center md:text-left">
          The one complete platform to do it all
        </h2>

        <div className="mb-10 md:mb-16 font-['Plus_Jakarta_Sans',Helvetica] text-xl md:text-[28px] tracking-[-0.56px] text-center md:text-left">
          {sentences.map((sentence, index) => (
            <button
              key={index}
              onClick={() => setActiveIndex(index)}
              className={`${
                index === activeIndex ? "text-[#6e56cf] font-medium" : "text-[#000000b2]"
              } tracking-[-0.16px] hover:opacity-80 transition-opacity cursor-pointer mr-4 last:mr-0`}
            >
              {sentence}
            </button>
          ))}
        </div>

        {/* Carousel container with relative positioning */}
        <div className="relative h-[320px] md:h-[400px]">
          <div className="flex justify-center items-stretch px-[100px] md:px-[175px]">
            {/* Cards */}
            {cardImages.map((card, index) => (
              <Card
                key={card.id}
                className="absolute w-[180px] md:w-[500px] h-[240px] md:h-[420px] transition-all duration-500 ease-out rounded-2xl overflow-hidden shadow-lg p-0 cursor-pointer"
                style={getCardStyles(index)}
                onClick={() => setActiveIndex(index)}
              >
                <CardContent className="p-0 h-full">
                  <img
                    className="w-full h-full object-cover transition-all duration-500"
                    alt={card.alt}
                    src={card.src || "/placeholder.svg"}
                  />
                  <div
                    className={`absolute inset-0 flex items-end justify-center p-4 bg-gradient-to-t from-black/40 to-transparent transition-opacity duration-500 ${index === activeIndex ? "opacity-100" : "opacity-0"}`}
                  ></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
