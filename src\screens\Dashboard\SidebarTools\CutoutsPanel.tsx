import React from 'react';

interface CutoutItem {
  id: string; // Or a more specific type if you have one for cutouts
  imageDataUrl: string;
  name?: string; // Optional name for the cutout
}

interface CutoutsPanelProps {
  cutouts: CutoutItem[];
  onSelectCutout?: (cutout: CutoutItem) => void; // Optional: if cutouts can be interacted with
  onDeleteCutout?: (cutoutId: string) => void; // Optional: to delete a cutout
}

export function CutoutsPanel({ 
  cutouts, 
  onSelectCutout,
  onDeleteCutout 
}: CutoutsPanelProps) {
  if (!cutouts || cutouts.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>No cutouts generated yet.</p>
        <p className="text-sm mt-1">Use the Smart Select tool and finalize a selection to create a cutout.</p>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-3">
      <h3 className="text-lg font-semibold text-gray-700 mb-2">Cut Outs</h3>
      <div className="grid grid-cols-2 gap-3">
        {cutouts.map((cutout) => (
          <div 
            key={cutout.id}
            className="relative group border border-gray-200 rounded-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow duration-150 aspect-square flex items-center justify-center bg-gray-50"
            onClick={() => {
              if (onSelectCutout) {
                onSelectCutout(cutout);
              }
            }}
            tabIndex={0}
            onKeyDown={(e) => {
              if ((e.key === 'Enter' || e.key === ' ') && onSelectCutout) {
                onSelectCutout(cutout);
              }
            }}
            aria-label={cutout.name || `Cutout ${cutout.id}`}
          >
            <img 
              src={cutout.imageDataUrl} 
              alt={cutout.name || `Cutout ${cutout.id}`} 
              className="max-w-full max-h-full object-contain"
            />
            {onDeleteCutout && (
              <button 
                onClick={(e) => {
                  e.stopPropagation(); // Prevent onClick of parent div
                  onDeleteCutout(cutout.id);
                }}
                className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-150 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400"
                aria-label={`Delete ${cutout.name || `Cutout ${cutout.id}`}`}
              >
                ✕
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
} 