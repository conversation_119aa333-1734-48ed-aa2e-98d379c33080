import { useSelector } from "react-redux"
import type { RootState } from "@/types/store.types"
import { APP_CONFIG } from "@/lib/config/app-config"
import {
  LifestylePhotographyCanvas,
  SketchToRenderCanvas,
  ComingSoonCanvas,
  BackgroundGeneratorCanvas,
  ProductPhotoCanvas,
  PrintsAndPatternsCanvas,
  TechnicalDrawingsCanvas,
  AvatarToPhotorealismCanvas,
  StartFromScratchCanvas,
  GraphicsAndPlacementCanvas,
  PrintOrFabricCanvas,
} from "../canvas"
import { useMemo } from "react"

interface ToolLayoutProps {
  children: React.ReactNode
}

export function ToolLayout({ children }: ToolLayoutProps) {
  const activeApp = useSelector((state: RootState) => state.app.activeApp)
  const showCanvas = useMemo(() => 
    APP_CONFIG[activeApp]?.showCanvas ?? false,
    [activeApp]
  )
  
  const Canvas = useMemo(() => {
    if (!showCanvas) return null
    switch (activeApp) {
      case "lifestyle-photography":
        return LifestylePhotographyCanvas
      case "sketch-to-render":
        return SketchToRenderCanvas
      case "background-generator":
        return BackgroundGeneratorCanvas
      case "product-photography":
        return ProductPhotoCanvas
      case "prints-and-patterns":
        return PrintsAndPatternsCanvas
      case "technical-drawings":
        return TechnicalDrawingsCanvas
      case "avatar-to-photorealism":
        return AvatarToPhotorealismCanvas
      case "start-from-scratch":
        return StartFromScratchCanvas
      case "graphics-and-placement":
        return GraphicsAndPlacementCanvas
      case "apply-print-or-fabric":
        return PrintOrFabricCanvas
      default:
        return ComingSoonCanvas
    }
  }, [activeApp, showCanvas])

  return (
    <div className="flex h-full">
      <div className="flex-1 flex">
        <div className="w-[360px] flex-shrink-0"> 
          {children}
        </div>
        {Canvas && <Canvas />}
      </div>
    </div>
  )
}





