import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";

interface MaterialEditProps {
    applyMaterial: (params: {
        file: File;
        strength: number;
        guidance: number;
    }) => void;
    resetMaterial: () => void;
}

export function MaterialEdit({
    applyMaterial,
    resetMaterial,
}: MaterialEditProps) {
    const [materialFile, setMaterialFile] = useState<File | null>(null);
    const [materialStrength, setMaterialStrength] = useState<number>(50);
    const [guidanceScale, setGuidanceScale] = useState<number>(2.5);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files?.[0]) {
            setMaterialFile(e.target.files[0]);
        }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        if (e.dataTransfer.files?.[0]) {
            setMaterialFile(e.dataTransfer.files[0]);
        }
    };

    const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
    };

    const handleApplyClick = () => {
        if (materialFile) {
            applyMaterial({
                file: materialFile,
                strength: materialStrength,
                guidance: guidanceScale,
            });
        }
    };

    const handleResetClick = () => {
        setMaterialFile(null);
        setMaterialStrength(50);
        setGuidanceScale(2.5);
        resetMaterial();
    };

    return (
        <div className="space-y-4">
            <h3 className="text-xl font-semibold">Apply Material</h3>

            <div
                className="cursor-pointer flex items-center gap-2 px-4 py-2 border rounded-lg hover:bg-gray-50"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() =>
                    document.getElementById("materialFileInput")?.click()
                }
            >
                <input
                    id="materialFileInput"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleFileChange}
                />
                {materialFile ? (
                    <p className="text-gray-700">{materialFile.name}</p>
                ) : (
                    <p className="text-gray-500">Select or drop file</p>
                )}
            </div>

            <div className="space-y-4">
                <div>
                    <Label
                        htmlFor="materialStrength"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Material Strength: {materialStrength}%
                    </Label>
                    <Slider
                        value={[materialStrength]}
                        min={0}
                        max={100}
                        step={1}
                        onValueChange={(value) => setMaterialStrength(value[0])}
                        className="w-full py-0.5"
                        classNames={{
                            track: "bg-[#e2e8f0] h-1.5",
                            range: "bg-[#6938ef] h-1.5",
                            thumb: "h-3.5 w-3.5 border-[#6938ef] bg-white hover:bg-[#6938ef]/10",
                        }}
                    />
                </div>

                <div>
                    <Label
                        htmlFor="guidanceScale"
                        className="block text-sm font-medium text-gray-700"
                    >
                        Guidance Scale: {guidanceScale}
                    </Label>
                    <Slider
                        value={[guidanceScale]}
                        min={0}
                        max={10}
                        step={0.1}
                        onValueChange={(value) => setGuidanceScale(value[0])}
                        className="w-full py-0.5"
                        classNames={{
                            track: "bg-[#e2e8f0] h-1.5",
                            range: "bg-[#6938ef] h-1.5",
                            thumb: "h-3.5 w-3.5 border-[#6938ef] bg-white hover:bg-[#6938ef]/10",
                        }}
                    />
                </div>
            </div>

            <div className="flex space-x-4">
                <Button
                    className="w-full"
                    variant="outline"
                    onClick={handleResetClick}
                >
                    Reset
                </Button>
                <Button
                    className="w-full"
                    onClick={handleApplyClick}
                    disabled={!materialFile}
                >
                    Apply
                </Button>
            </div>
        </div>
    );
}
