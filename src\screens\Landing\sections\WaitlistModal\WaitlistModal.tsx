import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    <PERSON>alogTitle,
    <PERSON>alogTrigger,
    DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";

// Form validation schema
const formSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().email("Invalid email address"),
    phone: z.string().min(1, "Phone number is required"),
    companyName: z.string().min(1, "Company name is required"),
    orgSize: z.string().min(1, "Organization size is required"),
});

type FormValues = z.infer<typeof formSchema>;

const GOOGLE_FORM_ACTION = import.meta.env.VITE_GOOGLE_FORM_ACTION;
const GOOGLE_FORM_FIELD_IDS = {
    firstName: import.meta.env.VITE_GOOGLE_FORM_FIRST_NAME,
    lastName: import.meta.env.VITE_GOOGLE_FORM_LAST_NAME,
    email: import.meta.env.VITE_GOOGLE_FORM_EMAIL,
    phone: import.meta.env.VITE_GOOGLE_FORM_PHONE,
    companyName: import.meta.env.VITE_GOOGLE_FORM_COMPANY_NAME,
    orgSize: import.meta.env.VITE_GOOGLE_FORM_ORG_SIZE,
};

type WaitlistModalProps = {
    prefilledEmail?: string;
};

export default function WaitlistModal({ prefilledEmail = "" }: WaitlistModalProps) {
    const [open, setOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const {
        register,
        handleSubmit,
        reset,
        setValue,
        formState: { errors },
    } = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            email: prefilledEmail,
        },
    });

    // Update email field when prefilledEmail prop changes
    useEffect(() => {
        if (prefilledEmail) {
            setValue("email", prefilledEmail);
        }
    }, [prefilledEmail, setValue]);

    const onSubmit = async (data: FormValues) => {
        setIsSubmitting(true);

        try {
            const formData = new FormData();
            formData.append(GOOGLE_FORM_FIELD_IDS.firstName, data.firstName);
            formData.append(GOOGLE_FORM_FIELD_IDS.lastName, data.lastName);
            formData.append(GOOGLE_FORM_FIELD_IDS.email, data.email);
            formData.append(GOOGLE_FORM_FIELD_IDS.phone, data.phone);
            formData.append(
                GOOGLE_FORM_FIELD_IDS.companyName,
                data.companyName
            );
            formData.append(GOOGLE_FORM_FIELD_IDS.orgSize, data.orgSize);

            await fetch(GOOGLE_FORM_ACTION, {
                method: "POST",
                mode: "no-cors",
                body: formData,
            });

            toast.success("You've been added to our waitlist!", {
                description: "We'll be in touch soon!",
            });

            reset();
            setOpen(false);
        } catch (error) {
            console.error(error);
            toast.error("Something went wrong", {
                description: "Please try again later.",
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button
                    size="lg"
                    className="w-full sm:w-auto font-semibold bg-[#6e56cf] text-white hover:bg-[#5b4bc2] h-10 px-8"
                >
                    Join Waitlist &rarr;
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle className="text-2xl">
                        Join Our Waitlist
                    </DialogTitle>
                    <DialogDescription>
                        Be the first to know when we launch. Fill out the form
                        below to secure your spot.
                    </DialogDescription>
                </DialogHeader>

                <form
                    onSubmit={handleSubmit(onSubmit)}
                    className="space-y-4 py-4"
                >
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="firstName">First Name</Label>
                            <Input
                                id="firstName"
                                {...register("firstName")}
                                className={
                                    errors.firstName ? "border-red-500" : ""
                                }
                            />
                            {errors.firstName && (
                                <p className="text-red-500 text-xs">
                                    {errors.firstName.message}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="lastName">Last Name</Label>
                            <Input
                                id="lastName"
                                {...register("lastName")}
                                className={
                                    errors.lastName ? "border-red-500" : ""
                                }
                            />
                            {errors.lastName && (
                                <p className="text-red-500 text-xs">
                                    {errors.lastName.message}
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                            id="email"
                            type="email"
                            {...register("email")}
                            className={errors.email ? "border-red-500" : ""}
                        />
                        {errors.email && (
                            <p className="text-red-500 text-xs">
                                {errors.email.message}
                            </p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                            id="phone"
                            type="tel"
                            {...register("phone")}
                            className={errors.phone ? "border-red-500" : ""}
                        />
                        {errors.phone && (
                            <p className="text-red-500 text-xs">
                                {errors.phone.message}
                            </p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="companyName">Company Name</Label>
                        <Input
                            id="companyName"
                            {...register("companyName")}
                            className={
                                errors.companyName ? "border-red-500" : ""
                            }
                        />
                        {errors.companyName && (
                            <p className="text-red-500 text-xs">
                                {errors.companyName.message}
                            </p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="orgSize">Organization Size</Label>
                        <Select
                            defaultValue=""
                            {...register("orgSize")}
                            onValueChange={(value) => {
                                register("orgSize").onChange({
                                    target: { name: "orgSize", value },
                                });
                            }}
                        >
                            <SelectTrigger
                                className={
                                    errors.orgSize ? "border-red-500" : ""
                                }
                            >
                                <SelectValue placeholder="Select organization size" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="1-10">
                                    1-10 employees
                                </SelectItem>
                                <SelectItem value="11-50">
                                    11-50 employees
                                </SelectItem>
                                <SelectItem value="51-200">
                                    51-200 employees
                                </SelectItem>
                                <SelectItem value="201-500">
                                    201-500 employees
                                </SelectItem>
                                <SelectItem value="501+">
                                    501+ employees
                                </SelectItem>
                            </SelectContent>
                        </Select>
                        {errors.orgSize && (
                            <p className="text-red-500 text-xs">
                                {errors.orgSize.message}
                            </p>
                        )}
                    </div>

                    <DialogFooter>
                        <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="w-full bg-[#6e56cf] text-white hover:bg-[#5b4bc2]"
                        >
                            {isSubmitting ? "Submitting..." : "Join Waitlist"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
