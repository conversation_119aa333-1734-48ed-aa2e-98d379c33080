import {
    SearchIcon,
    ShoppingBagIcon,
    Loader2Icon,
    GlobeIcon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Switch } from "@/components/ui/switch";
import { useState, useEffect, useRef } from "react";
import { searchTrends, getSimilarProducts, searchGlobalTrends } from "@/lib/api/index";
import { images } from "@/constants";
import { ProductModal } from "./ProductModal";
// Components
import TrendingSection from "./TrendingSection ";
import ProductGrid from "./ProductGrid";

// Data for trending items
const trendingItems = [
    { id: 1, text: "beach neutrals", image: images.beachNeutralsImage },
    { id: 2, text: "flirty black dress", image: images.flirtyBlackDress },
    { id: 3, text: "tank tops", image: images.tankTops },
    { id: 4, text: "kaftans", image: images.kaftans },
    { id: 5, text: "clean girl fits", image: images.cleanGirlFits },
    { id: 6, text: "abstract print", image: images.abstractPrintImage },
];

// Random fashion search terms for explore section
const randomSearchTerms = [
    "summer dress",
    "casual wear",
    "streetwear",
    "formal attire",
    "beach wear",
    "party dress",
    "athleisure",
    "bohemian style",
    "minimalist fashion",
    "vintage clothing",
];

export const Discover = () => {
    const [searchTerm, setSearchTerm] = useState("");
    const [searchResults, setSearchResults] = useState([]);
    const [exploreProducts, setExploreProducts] = useState([]);
    const [loading, setLoading] = useState(false);
    const [exploreLoading, setExploreLoading] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [similarProducts, setSimilarProducts] = useState([]);
    const [loadingSimilar, setLoadingSimilar] = useState(false);
    const [modalOpen, setModalOpen] = useState(false);
    const [showTrendingAndExplore, setShowTrendingAndExplore] = useState(true);
    // State for international toggle
    const [isInternational, setIsInternational] = useState(false);
    
    // Add request tracking to handle race conditions
    const [searchRequestId, setSearchRequestId] = useState(0);
    const [exploreRequestId, setExploreRequestId] = useState(0);
    const currentSearchRequestId = useRef(0);
    const currentExploreRequestId = useRef(0);

    // For infinite scroll
    const observerTarget = useRef(null);

    const getRandomSearchTerm = () => {
        const randomIndex = Math.floor(
            Math.random() * randomSearchTerms.length
        );
        return randomSearchTerms[randomIndex];
    };

    const fetchExploreProducts = async () => {
        // If already loading, don't start a new request
        if (exploreLoading) return;
        
        // Create a new request ID for this specific fetch
        const thisRequestId = exploreRequestId + 1;
        setExploreRequestId(thisRequestId);
        currentExploreRequestId.current = thisRequestId;
        
        // Store the current state of isInternational
        const currentIsInternational = isInternational;
        
        setExploreLoading(true);
        try {
            const randomTerm = getRandomSearchTerm();
            // Choose API based on isInternational flag
            const results = currentIsInternational 
                ? await searchGlobalTrends(randomTerm)
                : await searchTrends(randomTerm);
            
            // Only process results if this is still the current request
            if (thisRequestId === currentExploreRequestId.current) {
                // Transform the results if they're just an array of URLs
                const formattedResults = Array.isArray(results) && typeof results[0] === 'string'
                    ? results.map((url, index) => ({
                        id: `explore-${Date.now()}-${index}`, // Ensure unique IDs with timestamp
                        image_url: url,
                        similarity_index: index.toString(),
                        title: randomTerm,
                        price: null,
                        source: 'explore',
                        isGlobal: currentIsInternational,
                        product_link: null // Add default product_link field
                    }))
                    : results.map((item, index) => ({
                        ...item,
                        id: item.id || `explore-${Date.now()}-${index}`,
                        isGlobal: currentIsInternational,
                        // Preserve product_link if it exists in the API response
                        product_link: item.product_link || null
                    }));
                
                setExploreProducts((prevProducts) => [...prevProducts, ...formattedResults]);
            } else {
                // If this is an outdated request, ignore the results
                console.log("Ignoring outdated explore results", thisRequestId, currentExploreRequestId.current);
            }
        } catch (error) {
            // Only show errors for the current request
            if (thisRequestId === currentExploreRequestId.current) {
                console.error("Failed to fetch explore products:", error);
            }
        } finally {
            // Only update loading state for the current request
            if (thisRequestId === currentExploreRequestId.current) {
                setExploreLoading(false);
            }
        }
    };

    // Handle changes to the international toggle
    useEffect(() => {
        // Invalidate all current requests by incrementing their IDs
        const newSearchId = searchRequestId + 1;
        const newExploreId = exploreRequestId + 1;
        
        setSearchRequestId(newSearchId);
        setExploreRequestId(newExploreId);
        
        currentSearchRequestId.current = newSearchId;
        currentExploreRequestId.current = newExploreId;
        
        // Reset loading states
        setLoading(false);
        setExploreLoading(false);
        
        // Clear existing explore products when toggling
        setExploreProducts([]);
        
        // If there's an active search, re-run it with the new setting
        if (searchTerm && !showTrendingAndExplore) {
            handleSearch(searchTerm);
        } else {
            // Otherwise just fetch new explore products
            fetchExploreProducts();
        }
    }, [isInternational]);

    // Initial load of explore products - only once on component mount
    useEffect(() => {
        fetchExploreProducts();
    }, []);

    // Improved infinite scroll for explore section
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && showTrendingAndExplore && !exploreLoading) {
                    fetchExploreProducts();
                }
            },
            {
                // Trigger when element is 500px before it comes into view for better UX
                rootMargin: "500px",
                threshold: 0.1,
            }
        );

        if (observerTarget.current) {
            observer.observe(observerTarget.current);
        }

        return () => {
            observer.disconnect();
        };
    }, [showTrendingAndExplore, exploreLoading, isInternational]);

    const handleSearch = async (term) => {
        if (!term.trim()) return;
        
        // Create a new request ID for this specific search
        const thisRequestId = searchRequestId + 1;
        setSearchRequestId(thisRequestId);
        currentSearchRequestId.current = thisRequestId;
        
        // Store the current state of isInternational
        const currentIsInternational = isInternational;

        setLoading(true);
        setShowTrendingAndExplore(false);
        setSearchTerm(term);
        
        // Clear previous search results immediately
        setSearchResults([]);

        try {
            // Choose API based on isInternational flag
            const results = currentIsInternational 
                ? await searchGlobalTrends(term)
                : await searchTrends(term);
            
            // Only process results if this is still the current request
            if (thisRequestId === currentSearchRequestId.current) {
                // Transform the results if they're just an array of URLs
                const formattedResults = Array.isArray(results) && typeof results[0] === 'string'
                    ? results.map((url, index) => ({
                        id: `search-${Date.now()}-${index}`,
                        image_url: url,
                        similarity_index: index.toString(),
                        title: term,
                        price: null,
                        source: 'search',
                        isGlobal: currentIsInternational,
                        product_link: null // Add default product_link field
                    }))
                    : results.map((item, index) => ({
                        ...item,
                        id: item.id || `search-${Date.now()}-${index}`,
                        isGlobal: currentIsInternational,
                        // Preserve product_link if it exists in the API response
                        product_link: item.product_link || null
                    }));
                
                setSearchResults(formattedResults);
            } else {
                // If this is an outdated request, ignore the results
                console.log("Ignoring outdated search results", thisRequestId, currentSearchRequestId.current);
            }
        } catch (error) {
            // Only show errors for the current request
            if (thisRequestId === currentSearchRequestId.current) {
                console.error("Search failed:", error);
            }
        } finally {
            // Only update loading state for the current request
            if (thisRequestId === currentSearchRequestId.current) {
                setLoading(false);
            }
        }
    };

    const handleProductClick = async (product) => {
        setSelectedProduct(product);
        setModalOpen(true);
        
        // Reset similar products when opening new product
        setSimilarProducts([]);
        setLoadingSimilar(true);

        try {
            const similar = await getSimilarProducts(product.similarity_index);
            setSimilarProducts(similar);
        } catch (error) {
            console.error("Failed to fetch similar products:", error);
        } finally {
            setLoadingSimilar(false);
        }
    };

    // Handle international toggle change
    const handleInternationalToggle = (checked) => {
        setIsInternational(checked);
        // The useEffect will handle fetching new data and cancelling old requests
    };

    return (
        <section className="w-full h-full p-6 bg-white overflow-y-auto">
            <div className="max-w-5xl mx-auto flex flex-col gap-6">
                {/* Header section */}
                <SearchHeader 
                    searchTerm={searchTerm}
                    setSearchTerm={setSearchTerm}
                    handleSearch={handleSearch}
                    loading={loading}
                    isInternational={isInternational}
                    onInternationalToggle={handleInternationalToggle}
                />

                {/* Trending section - Only show when no search results */}
                {showTrendingAndExplore && (
                    <TrendingSection 
                        trendingItems={trendingItems} 
                        handleSearch={handleSearch} 
                    />
                )}
            </div>

            {/* Search Results Grid */}
            {!showTrendingAndExplore && (
                <div className="max-w-5xl mx-auto mt-10">
                    <div className="flex items-center gap-2 mb-7">
                        <SearchIcon className="w-4 h-4" />
                        <span className="font-text-sm-medium text-slate-950">
                            {isInternational ? "Global Search Results" : "Search Results"}
                        </span>
                    </div>
                    
                    {loading && (
                        <div className="flex justify-center items-center py-20">
                            <Loader2Icon className="animate-spin h-8 w-8 text-brand" />
                            <span className="ml-3 text-slate-700">
                                {isInternational ? "Searching globally..." : "Searching..."}
                            </span>
                        </div>
                    )}
                    
                    {!loading && searchResults.length > 0 && (
                        <ProductGrid 
                            products={searchResults} 
                            onProductClick={handleProductClick}
                            prefix="search"
                        />
                    )}
                    
                    {!loading && searchResults.length === 0 && (
                        <div className="text-center py-20">
                            <p className="text-slate-700">No results found for "{searchTerm}"</p>
                            <Button 
                                className="mt-4"
                                onClick={() => {
                                    setShowTrendingAndExplore(true);
                                    setSearchTerm("");
                                }}
                            >
                                Return to Explore
                            </Button>
                        </div>
                    )}
                </div>
            )}

            {/* Explore section with infinite scroll */}
            {showTrendingAndExplore && (
                <div className="max-w-5xl mx-auto mt-10">
                    <div className="flex items-center gap-2 mb-7">
                        <ShoppingBagIcon className="w-4 h-4" />
                        <span className="font-text-sm-medium text-slate-950">
                            {isInternational ? "Global Explore" : "Explore"}
                        </span>
                    </div>

                    <ProductGrid 
                        products={exploreProducts} 
                        onProductClick={handleProductClick}
                        prefix="explore"
                    />

                    {/* Infinite scroll trigger */}
                    <div
                        ref={observerTarget}
                        className="h-10 mt-5 mb-20" // Added bottom margin for buffer
                    >
                        {exploreLoading && (
                            <div className="text-center text-gray-500 flex items-center justify-center">
                                <Loader2Icon className="animate-spin mr-2 h-4 w-4" />
                                Loading more items...
                            </div>
                        )}
                    </div>
                </div>
            )}

            <ProductModal
                open={modalOpen}
                onOpenChange={setModalOpen}
                selectedProduct={selectedProduct}
                similarProducts={similarProducts}
                loadingSimilar={loadingSimilar}
                onProductClick={handleProductClick}
            />
        </section>
    );
};

const SearchHeader = ({ 
    searchTerm, 
    setSearchTerm, 
    handleSearch, 
    loading, 
    isInternational, 
    onInternationalToggle 
}) => {
    return (
        <div className="flex flex-col gap-4 w-full">
            <h1 className="font-semibold text-slate-950 text-2xl">
                Discover the latest trends
            </h1>

            <div className="flex items-center gap-3 w-full justify-between">
                <div className="flex-1 relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                        <SearchIcon className="w-4 h-4 text-gray-500" />
                    </div>
                    <Input
                        className="pl-9 py-2 pr-3 w-full rounded-md border border-solid border-slate-200 shadow-shadow-xs text-sm"
                        placeholder="boss girl aesthetic"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                handleSearch(searchTerm);
                            }
                        }}
                    />
                </div>

                <Button
                    className="bg-brand text-white font-medium px-4 py-2 rounded-md shadow-shadow-xs flex items-center gap-1.5 text-sm h-[42px]"
                    onClick={() => handleSearch(searchTerm)}
                    disabled={loading}
                >
                    {loading ? (
                        <span className="flex items-center">
                            <Loader2Icon className="animate-spin mr-2 h-4 w-4" />
                            Searching...
                        </span>
                    ) : (
                        <>
                            <img
                                src={images.nounArrow}
                                alt="Search"
                                className="w-3.5 h-3.5"
                            />
                            Search
                        </>
                    )}
                </Button>

                <Button
                    variant="outline"
                    size="icon"
                    className="w-[42px] h-[42px] p-0 rounded-md border-none shadow-none bg-transparent hover:bg-transparent"
                >
                    <img
                        className="w-full h-full"
                        alt="Lens"
                        src={images.lensButton}
                    />
                </Button>
            </div>
            
            <div className="flex items-center gap-3">
                <ToggleGroup
                    type="single"
                    defaultValue="women"
                    className="border border-solid border-[#d5d6d9] rounded-md shadow-shadow-xs"
                >
                    <ToggleGroupItem
                        value="women"
                        className="px-3 py-1.5 text-sm text-gray-700 font-medium bg-brand-100 border-r border-[#d5d6d9]"
                    >
                        Women
                    </ToggleGroupItem>
                    <ToggleGroupItem
                        value="men"
                        className="px-3 py-1.5 text-sm text-gray-700 font-medium bg-white border-r border-[#d5d6d9]"
                    >
                        Men
                    </ToggleGroupItem>
                    <ToggleGroupItem
                        value="kids"
                        className="px-3 py-1.5 text-sm text-gray-700 font-medium bg-white"
                    >
                        Kids
                    </ToggleGroupItem>
                </ToggleGroup>
                
                <div className="flex items-center gap-2 h-[42px] px-3 border border-solid border-[#d5d6d9] rounded-md shadow-shadow-xs">
                    <GlobeIcon className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-700 font-medium">Global</span>
                    <Switch 
                        aria-label="Toggle international search"
                        checked={isInternational}
                        onCheckedChange={onInternationalToggle}
                    />
                </div>
            </div>
        </div>
    );
};

export default Discover;