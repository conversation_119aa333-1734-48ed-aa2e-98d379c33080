import { AppState, AppType, Tab, ToolConfigurationState } from "@/types/app.types";
import { createSlice, type PayloadAction } from "@reduxjs/toolkit"


const defaultTab: Tab = {
  id: "dashboard-tab-1",
  type: "dashboard",
  title: "AI Tools",
};

const initialState: AppState = {
  activeApp: "discover",
  activeTabId: defaultTab.id,
  tabs: [defaultTab],
  generatedImages: {},
  isLoading: {
    images: {}
  },
  error: {
    images: null
  },
  toolConfigurations: {}
}

const appSlice = createSlice({
  name: "app",
  initialState,
  reducers: {
    setActiveApp: (state, action: PayloadAction<AppType>) => {
      state.activeApp = action.payload
    },
    updateTab: (state, action: PayloadAction<{ id: string; type: AppType; title: string }>) => {
      const tabIndex = state.tabs.findIndex(tab => tab.id === action.payload.id)
      if (tabIndex !== -1) {
        state.tabs[tabIndex] = action.payload
        state.activeTabId = action.payload.id
        state.activeApp = action.payload.type
      }
    },
    addTab: (state, action: PayloadAction<{ id: string; type: AppType; title: string }>) => {
      // Don't add a new "dashboard" tab if one already exists
      if (action.payload.type === "dashboard" && 
          state.tabs.some(tab => tab.type === "dashboard")) {
        return
      }
      state.tabs.push(action.payload)
      state.activeTabId = action.payload.id
      state.activeApp = action.payload.type
    },
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTabId = action.payload
      const tab = state.tabs.find((tab) => tab.id === action.payload)
      if (tab) {
        state.activeApp = tab.type
      }
    },
    closeTab: (state, action: PayloadAction<string>) => {
      const index = state.tabs.findIndex((tab) => tab.id === action.payload);
      if (index !== -1) {
        state.tabs.splice(index, 1);
        // Clean up tab-specific state
        delete state.generatedImages[action.payload];
        delete state.isLoading.images[action.payload];
        delete state.toolConfigurations[action.payload];
        
        if (state.activeTabId === action.payload) {
          if (state.tabs.length > 0) {
            state.activeTabId = state.tabs[state.tabs.length - 1].id;
            state.activeApp = state.tabs[state.tabs.length - 1].type;
          } else {
            state.activeTabId = null;
            state.activeApp = "dashboard";
          }
        }
      }
    },
    setGeneratedImages: (state, action: PayloadAction<{ tabId: string; images: string[] }>) => {
      const { tabId, images } = action.payload;
      state.generatedImages[tabId] = images;
      state.isLoading.images[tabId] = false;
    },
    startGeneratingImages: (state, action: PayloadAction<string>) => {
      const tabId = action.payload;
      state.isLoading.images[tabId] = true;
    },
    setImagesError: (state, action: PayloadAction<string>) => {
      state.error.images = action.payload;
      // Reset loading state for all tabs
      state.isLoading.images = {};
    },
    clearGeneratedImages: (state, action: PayloadAction<string>) => {
      const tabId = action.payload
      delete state.generatedImages[tabId]
    },
    
    updateToolConfiguration: (
      state,
      action: PayloadAction<{
        tabId: string;
        values: Partial<ToolConfigurationState>;
      }>
    ) => {
      const { tabId, values } = action.payload;
      state.toolConfigurations[tabId] = {
        ...state.toolConfigurations[tabId],
        ...values
      };
    }
  },
})

export const {
  setActiveApp,
  addTab,
  updateTab,
  setActiveTab,
  closeTab,
  setGeneratedImages,
  startGeneratingImages,
  setImagesError,
  clearGeneratedImages,
  updateToolConfiguration
} = appSlice.actions
export default appSlice.reducer









