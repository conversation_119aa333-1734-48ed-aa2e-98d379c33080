import api from '@/lib/api/api';

// Helper function for base64 to blob conversion for image generation plugins
function base64ToBlob(base64) {
  try {
    const parts = base64.split(';base64,');

    const contentType = parts[0].split(':')[1] || 'image/png';

    const raw = window.atob(parts[1]);

    const uInt8Array = new Uint8Array(raw.length);
    for (let i = 0; i < raw.length; ++i) {
      uInt8Array[i] = raw.charCodeAt(i);
    }

    const blob = new Blob([uInt8Array], { type: contentType });
    
    return blob;
  } catch (error) {
    console.error('Base64 to Blob conversion error:', error);
    throw error;
  }
}

// Add new function for global search
export async function searchGlobalTrends(search_term) {
  try {
    const response = await api.post('/trends/global/search', {
      search_term
    });
    return response.data.data;
  } catch (error) {
    console.error('Global Search Trends Error:', error);
    throw error;
  }
}

async function generatePluginResponse(promptMessage, pluginId, base64Image = undefined, temperature = 0.5, imageDescription = "") {
  try {

    const response = await api.post(`/pluginPrompt`, {
      promptMessage,
      pluginId,
      base64EncodedImage: base64Image,
      imageDescription: imageDescription,
      temperature: temperature
    });

    if (response.data.status === 'success') {
      const base64Data = response.data.data.generatedImage;
      
      const blob = base64ToBlob(base64Data);

      const url = URL.createObjectURL(blob);
      return url;
    }
    throw new Error(response.data.message || `Failed to generate ${pluginId} image`);
  } catch (error) {
    console.error('API Call Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    throw error;
  }
}

export async function improvePrompt(toolTitle: string, description: string) {
  try {
    const response = await api.post('/improvePrompt', {
      workflow: toolTitle,
      text: description 
    });
    
    if (response.data.status === 'success') {
      return response.data.data.improvedPrompt;
    }
    throw new Error(response.data.message || 'Failed to improve prompt');
  } catch (error) {
    console.error('Failed to improve prompt:', error);
    throw error;
  }
}

export const generateLifestylePhoto = (promptMessage, base64Image, temperature = 0.5) => 
  generatePluginResponse(promptMessage, 'lp', base64Image, temperature);

export const generateSketchToRender = (promptMessage, base64Image, temperature = 0.5) => 
  generatePluginResponse(promptMessage, 's2r', base64Image, temperature);

export const generateBackgroundImage = (promptMessage, base64Image, temperature = 0.5) => 
  generatePluginResponse(promptMessage, 'bgg', base64Image, temperature);

export const generateProductPhoto = (promptMessage, base64Image, temperature = 0.5) => 
  generatePluginResponse(promptMessage, 'pp', base64Image, temperature);

export const generatePrintsAndPatterns = (promptMessage, base64Image, imageDescription, temperature = 0.5) => 
  generatePluginResponse(promptMessage, 'p&p', base64Image, temperature, imageDescription);

export const generateTechnicalDrawings = (promptMessage, base64Image, temperature = 0.5) => 
  generatePluginResponse(promptMessage, 'td', base64Image, temperature);

export const generateAvatarToPhotorealism = (promptMessage, base64Image, temperature = 0.5) => 
  generatePluginResponse(promptMessage, '3dap', base64Image, temperature);

export const generateStartFromScratch = (promptMessage, base64Image, temperature = 0.5) => 
  generatePluginResponse(promptMessage, 'sfs', base64Image, temperature);

export const generateGraphicsAndPlacement = (promptMessage, base64Image, temperature = 0.5) => 
  generatePluginResponse(promptMessage, 'g&pp', base64Image, temperature);


export const generateApplyPrintOrFabric = async (mode, promptMessage, productImage, fabricImage, temperature = 0.5, imageDescription = "") => {
  try {

    if (!productImage || !fabricImage) {
      throw new Error('Both product and fabric images are required');
    }
    
    const formattedProductImage = productImage.startsWith('data:') 
      ? productImage 
      : `data:image/png;base64,${productImage}`;
      
    const formattedFabricImage = fabricImage.startsWith('data:') 
      ? fabricImage 
      : `data:image/png;base64,${fabricImage}`;
    

    const response = await api.post(`/mix`, {
      mode: mode,
      base64EncodedImage1: formattedProductImage,
      base64EncodedImage2: formattedFabricImage,
      imageDescription1: imageDescription, // Use the product description from the describe API
      promptMessage: promptMessage,
      temperature: temperature
    });

    if (response.data.status === 'success') {

      const mixedImageData = response.data.data.generatedImage;

      if (typeof mixedImageData === 'string') {
        const dataUrl = mixedImageData.startsWith('data:') 
          ? mixedImageData 
          : `data:image/png;base64,${mixedImageData}`;
        return dataUrl;
      }
      
      console.error('Unexpected response format:', mixedImageData);
      throw new Error('Invalid response data format');
    }
    
    throw new Error(response.data.message || 'Failed to mix images');
  } catch (error) {
    console.error('API Call Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    throw error;
  }
}

export async function queryTrendGPT(query, history = []) {
  try {
    
    const response = await api.post('/trends/gpt', {
      query,
      history: history.map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    });

    if (response.data.status === 'success') {
      return {
        content: response.data.data.analysis,
        citations: response.data.data.citations || [],
        images: response.data.data.image_uris || []
      };
    }
    throw new Error(response.data.message || 'Failed to get TrendGPT response');
  } catch (error) {
    console.error('TrendGPT API Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    throw error;
  }
}

export async function searchTrends(search_term) {
  try {
    const response = await api.post('/trends/search', {
      search_term
    });
    return response.data.data;
  } catch (error) {
    console.error('Search Trends Error:', error);
    throw error;
  }
}

export async function getSimilarProducts(similarity_index) {
  try {
    const response = await api.post('/trends/similar_products', {
      similarity_index
    });
    return response.data.data;
  } catch (error) {
    if (error.response?.status === 500) {
      throw new Error('Server error while fetching similar products');
    }
    if (error.code === 'ERR_NETWORK') {
      throw new Error('Network error - CORS or connectivity issue');
    }
    throw error;
  }
}

export async function upscaleImage(base64Image: string) {
  try {
    // Format the image if needed
    const formattedImage = base64Image.startsWith('data:') 
      ? base64Image 
      : `data:image/png;base64,${base64Image}`;
      
    const response = await api.post('/upscale', {
      base64EncodedImage: formattedImage,
    });

    if (response.data.status === 'success') {
      return response.data.data.generatedImage;
    }
    
    throw new Error(response.data.message || 'Failed to upscale image');
  } catch (error) {
    console.error('API Call Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    throw error;
  }
}

export async function vectorizeImage(base64Image: string) {
  try {
    // Format the image if needed
    const formattedImage = base64Image.startsWith('data:') 
      ? base64Image 
      : `data:image/png;base64,${base64Image}`;
      
    const response = await api.post('/vectorize', {
      base64EncodedImage: formattedImage,
    });

    if (response.data.status === 'success') {
      return response.data.data.generatedImage;
    }
    
    throw new Error(response.data.message || 'Failed to upscale image');
  } catch (error) {
    console.error('API Call Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    throw error;
  }
}

export async function removeBackground(base64Image: string, temperature = 0.5) {
  try {
    // Format the image if needed
    const formattedImage = base64Image.startsWith('data:') 
      ? base64Image 
      : `data:image/png;base64,${base64Image}`;
      
    const response = await api.post('/pluginPrompt', {
      promptMessage: "Remove the background from this image",
      pluginId: "bgr",
      base64EncodedImage: formattedImage,
      temperature
    });

    if (response.data.status === 'success') {
      return response.data.data.generatedImage;
    }
    
    throw new Error(response.data.message || 'Failed to remove background');
  } catch (error) {
    console.error('API Call Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    throw error;
  }
}

export async function editImage({
  base64EncodedOriginalImage,
  base64EncodedMaskImage,
  base64EncodedMaskColorImage,
  promptMessage,
  mode,
  useCase,
}: {
  base64EncodedOriginalImage: string;
  base64EncodedMaskImage: string;
  base64EncodedMaskColorImage: string;
  promptMessage: string;
  mode: string;
  useCase: string;
}) {
  try {
    const response = await api.post('/edit', {
      base64EncodedOriginalImage,
      base64EncodedMaskImage,
      base64EncodedMaskColorImage,
      promptMessage,
      mode,
      useCase,
    });
    
    if (response.data.status === 'success') {
      return response.data.data.generatedImage;
    }

    throw new Error(response.data.message || 'Failed to edit image');
  } catch (error) {
    console.error('Edit Image API Error:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
    });
    throw error;
  }
}
