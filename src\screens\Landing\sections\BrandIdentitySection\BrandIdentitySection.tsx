import React from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { images } from "@/constants"

export const BrandIdentitySection = () => {
  const features = [
    {
      title: "Match your aesthetic",
      description: "AI models custom trained to represent your brand's unique style and aesthetic",
    },
    {
      title: "Make more best sellers",
      description: "Generate high-performing designs guided by trend data and customer insights",
    },
    {
      title: "Personalized for your team",
      description: "Design workflows tailored to your brand's categories, team, and use cases",
    },
    {
      title: "Create accurate techpacks",
      description: "Export ready-to-produce techpacks with specs, callouts, and BOM in seconds",
    },
  ]

  return (
    <section className="relative w-full px-2 md:px-10 py-16 md:py-24 bg-[#f1eef9]">
      <div className="container mx-auto px-4 md:px-6 max-w-[1440px]">
        <h1 className="font-['Plus_Jakarta_Sans',Helvetica] font-bold text-black text-3xl md:text-5xl tracking-[-0.96px] mb-8 md:mb-12">
          Built for your brand&apos;s identity
        </h1>

        <div className="flex flex-col md:flex-row gap-8 md:gap-16">
          <Card className="w-full md:w-[45%] rounded-[20px] overflow-hidden border-0 p-0">
            <CardContent className="p-0">
              <div className="relative w-full pb-[100%]">
                <img
                  className="absolute inset-0 w-full h-full object-cover"
                  alt="Brand Identity"
                  src={images.brandIdentity || "/placeholder.svg"}
                />
              </div>
            </CardContent>
          </Card>

          <div className="w-full md:w-[55%] flex flex-col text-left md:pl-4">
            {features.map((feature, index) => (
              <React.Fragment key={index}>
                <div className="flex flex-col items-start gap-[9px] relative w-full mb-4 md:mb-6">
                  <h2 className="relative self-stretch mt-[-1.00px] font-['Plus_Jakarta_Sans',Helvetica] font-bold text-[#000000d9] text-xl md:text-2xl leading-9">
                    {feature.title}
                  </h2>
                  <p className="relative self-stretch font-['Plus_Jakarta_Sans',Helvetica] font-medium text-[#000000b2] text-base md:text-lg leading-[27px]">
                    {feature.description}
                  </p>
                </div>
                {index < features.length - 1 && <Separator className="self-stretch mb-4 md:mb-6" />}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
