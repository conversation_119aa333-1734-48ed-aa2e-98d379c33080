## Project Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd rapidrunway
```

2. Install dependencies:
```bash
npm install
```

## Running the Project

### Main Branch
To run the development version:
```bash
git checkout main
npm install
npm run dev
```

The development server will start at `http://localhost:5173`

### Feature Branch

```bash
git checkout feature/your-feature-branch
npm install
npm run dev
```
