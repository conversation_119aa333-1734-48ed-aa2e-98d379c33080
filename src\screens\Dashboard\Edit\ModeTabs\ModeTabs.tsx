import { But<PERSON> } from "@/components/ui/button";
import { Wand2, <PERSON><PERSON>, <PERSON>rid, <PERSON><PERSON>, Settings2 } from "lucide-react";

const modes = [
    { id: "magic", icon: Wand2, label: "Magic Edit" },
    { id: "color", icon: Palette, label: "Color" },
    { id: "pattern", icon: Grid, label: "Add Pattern" },
    { id: "material", icon: Shirt, label: "Apply Material" },
    { id: "basic", icon: Settings2, label: "Basic Edit" },
];

export const ModeTabs = ({ selectedMode, setSelectedMode }) => (
    <div className="flex mb-6 justify-center items-center gap-6">
        {modes.map(({ id, icon: Icon, label }) => (
            <Button
                variant="ghost"
                key={id}
                onClick={() => setSelectedMode(id)}
                className={`p-2 rounded ${
                    selectedMode === id
                        ? "bg-purple-100 text-purple-600 p-1"
                        : "hover:bg-gray-100"
                }`}
                aria-label={label}
            >
                <Icon className="h-5 w-5" />
            </Button>
        ))}
    </div>
);
