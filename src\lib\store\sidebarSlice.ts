import { createSlice, type PayloadAction } from "@reduxjs/toolkit"

interface SidebarState {
  isCollapsed: boolean
  isHovering: boolean
}

const initialState: SidebarState = {
  isCollapsed: true,
  isHovering: false,
}

const sidebarSlice = createSlice({
  name: "sidebar",
  initialState,
  reducers: {
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.isCollapsed = action.payload
    },
    setHovering: (state, action: PayloadAction<boolean>) => {
      state.isHovering = action.payload
    },
  },
})

export const { setSidebarCollapsed, setHovering } = sidebarSlice.actions
export default sidebarSlice.reducer
