import { Button } from "@/components/ui/button";
import {
    HexAlphaColorPicker,
    RgbColorPicker,
    HslColorPicker,
} from "react-colorful";
import { useState, useEffect } from "react";
import { useDropzone } from 'react-dropzone';

//  Hex to RGBA object
const hexToRgbA = (hex: string) => {
    let c;
    if (/^#([A-Fa-f0-9]{3,4}){1,2}$/.test(hex)) {
        c = hex.substring(1).split("");
        if (c.length === 3) c = [c[0], c[0], c[1], c[1], c[2], c[2]];
        if (c.length === 6) c = [c[0], c[1], c[2], c[3], c[4], c[5], "F", "F"]; // Add default alpha if missing
        if (c.length === 8) {
            const r = parseInt(c[0] + c[1], 16);
            const g = parseInt(c[2] + c[3], 16);
            const b = parseInt(c[4] + c[5], 16);
            const a = parseInt(c[6] + c[7], 16) / 255;
            return { r, g, b, a };
        }
    }
    return { r: 0, g: 0, b: 0, a: 1 }; // Default to black if invalid
};

// RGBA object to Hex string
const rgbAToHex = (rgba: { r: number; g: number; b: number; a: number }) => {
    const toHex = (c: number) => Math.round(c).toString(16).padStart(2, "0");
    const r = toHex(rgba.r);
    const g = toHex(rgba.g);
    const b = toHex(rgba.b);
    const a = Math.round(rgba.a * 255)
        .toString(16)
        .padStart(2, "0");
    return `#${r}${g}${b}${a}`;
};

// RGBA object to HSL object
const rgbAToHslA = (rgba: { r: number; g: number; b: number; a: number }) => {
    const r = rgba.r / 255;
    const g = rgba.g / 255;
    const b = rgba.b / 255;
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0;
    let s = 0;
    const l = (max + min) / 2;

    if (max !== min) {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
            case r:
                h = (g - b) / d + (g < b ? 6 : 0);
                break;
            case g:
                h = (b - r) / d + 2;
                break;
            case b:
                h = (r - g) / d + 4;
                break;
        }
        h /= 6;
    }

    return { h: h * 360, s: s * 100, l: l * 100, a: rgba.a };
};

// HSL object to RGBA object
const hslAToRgbA = (hsla: { h: number; s: number; l: number; a: number }) => {
    const s = hsla.s / 100;
    const l = hsla.l / 100;
    const c = (1 - Math.abs(2 * l - 1)) * s;
    const x = c * (1 - Math.abs(((hsla.h / 60) % 2) - 1));
    const m = l - c / 2;
    let r = 0;
    let g = 0;
    let b = 0;

    if (hsla.h >= 0 && hsla.h < 60) {
        r = c;
        g = x;
        b = 0;
    } else if (hsla.h >= 60 && hsla.h < 120) {
        r = x;
        g = c;
        b = 0;
    } else if (hsla.h >= 120 && hsla.h < 180) {
        r = 0;
        g = c;
        b = x;
    } else if (hsla.h >= 180 && hsla.h < 240) {
        r = 0;
        g = x;
        b = c;
    } else if (hsla.h >= 240 && hsla.h < 300) {
        r = x;
        g = 0;
        b = c;
    } else if (hsla.h >= 300 && hsla.h < 360) {
        r = c;
        g = 0;
        b = x;
    }

    const red = Math.round((r + m) * 255);
    const green = Math.round((g + m) * 255);
    const blue = Math.round((b + m) * 255);

    return { r: red, g: green, b: blue, a: hsla.a };
};

interface ColorEditProps {
    color: string;
    setColor: (color: string) => void;
    applyFillColor: (color: string) => void;
    resetCanvas: () => void;
}

export function ColorEdit({
    color,
    setColor,
    applyFillColor,
    resetCanvas,
}: ColorEditProps) {
    const [displayColor, setDisplayColor] = useState(hexToRgbA(color));
    const [colorFormat, setColorFormat] = useState("Hex");
    const [extractedColorInfo, setExtractedColorInfo] = useState('');
    const [showCopiedMessage, setShowCopiedMessage] = useState(false);

    useEffect(() => {
        setDisplayColor(hexToRgbA(color));
        setExtractedColorInfo('');
    }, [color]);

    // Reset extracted color info when color format changes
    useEffect(() => {
        setExtractedColorInfo('');
    }, [colorFormat]);

    // Function to copy text to clipboard
    const handleCopyToClipboard = async (text: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setShowCopiedMessage(true);
            setTimeout(() => setShowCopiedMessage(false), 2000); // Hide message after 2 seconds
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    };

    // Function for handling file upload and dominant color extraction using canvas
    const handleFileUpload = (acceptedFiles: File[]) => {
        const file = acceptedFiles[0];
        if (!file || !file.type.startsWith('image/')) {
            alert('Please upload a valid image file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = async () => {
                try {
                    // Ensure image is decoded before processing
                    await img.decode();

                    // Create a temporary canvas to draw the image
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    if (!ctx) {
                         alert('Could not create canvas context.');
                         return;
                    }

                    canvas.width = img.width;
                    canvas.height = img.height;

                    // Draw the image onto the canvas
                    ctx.drawImage(img, 0, 0, img.width, img.height);

                    // Get the pixel data
                    const imageData = ctx.getImageData(0, 0, img.width, img.height).data;
                    const pixelCount = img.width * img.height;
                    const colorCounts: { [key: string]: number } = {};
                    let dominantColorRgb: [number, number, number] = [0, 0, 0];
                    let maxCount = 0;

                    // Iterate through all pixels and count colors
                    for (let i = 0; i < pixelCount; i++) {
                        const offset = i * 4;
                        const r = imageData[offset];
                        const g = imageData[offset + 1];
                        const b = imageData[offset + 2];
                        const a = imageData[offset + 3];

                        // Ignore transparent or nearly transparent pixels
                        if (a >= 125) { // Alpha threshold (e.g., ignore if less than ~50% opaque)
                            const rgb = `${r},${g},${b}`;
                            colorCounts[rgb] = (colorCounts[rgb] || 0) + 1;

                            // Update dominant color if count is higher
                            if (colorCounts[rgb] > maxCount) {
                                maxCount = colorCounts[rgb];
                                dominantColorRgb = [r, g, b];
                            }
                        }
                    }

                    // Convert the dominant RGB to an RGBA object, keeping the current alpha
                    const newRgba = {
                        r: dominantColorRgb[0],
                        g: dominantColorRgb[1],
                        b: dominantColorRgb[2],
                        a: displayColor.a, // Use the current alpha value
                    };

                    // Convert RGBA object to Hex string
                    const hex = rgbAToHex(newRgba);

                    // Update state
                    setColor(hex); // Update the parent color state
                    setDisplayColor(hexToRgbA(hex)); // Update local display

                    // Format and set the extracted color info for display
                    const formattedColor =
                        colorFormat === "Hex"
                            ? hex
                            : colorFormat === "RGB"
                            ? `rgba(${Math.round(
                                  newRgba.r
                              )}, ${Math.round(newRgba.g)}, ${Math.round(
                                  newRgba.b
                              )}, ${newRgba.a.toFixed(2)})`
                            : `hsla(${Math.round(
                                  rgbAToHslA(newRgba).h
                              )}, ${Math.round(
                                  rgbAToHslA(newRgba).s
                              )}%, ${Math.round(
                                  rgbAToHslA(newRgba).l
                              )}%, ${newRgba.a.toFixed(2)})`;
                    setExtractedColorInfo(formattedColor);

                    // Clean up temporary canvas (optional, but good practice)
                    // canvas.remove(); // Or document.body.removeChild(canvas) if appended

                } catch (error) {
                    console.error('Error extracting color:', error);
                    alert('Could not extract color from image.');
                }
            };
            img.onerror = () => {
                alert('Error loading image.');
            };
            img.src = e.target.result as string;
        };
        reader.onerror = () => {
            alert('Error reading file.');
        };
        reader.readAsDataURL(file);
    };

    // Configure react-dropzone
    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop: handleFileUpload,
        accept: {
            'image/*': ['.jpeg', '.png', '.gif', '.bmp', '.webp', '.jpg'], // Specify accepted image types
        },
        multiple: false, // Only allow one file at a time
    });

    const savedColors = [
        "#f44336",
        "#ff9800",
        "#ffeb3b",
        "#4caf50",
        "#00bcd4",
        "#2196f3",
        "#673ab7",
        "#e91e63",
        "#f44336",
        "#9c27b0",
    ];

    return (
        <div className="space-y-3">
            <h3 className="text-lg font-semibold">Color</h3>
            <div className="mb-4">
                <div className="text-sm text-gray-600">Fill Color</div>
                <div className="mb-4">
                    {colorFormat === "Hex" && (
                        <HexAlphaColorPicker
                            color={rgbAToHex(displayColor)}
                            onChange={(c) => setDisplayColor(hexToRgbA(c))}
                            style={{
                                width: "100%",
                                boxShadow: "none",
                                borderRadius: "0.5rem",
                            }}
                        />
                    )}
                    {colorFormat === "RGB" && (
                        <RgbColorPicker
                            color={displayColor}
                            onChange={(c) =>
                                setDisplayColor({ ...c, a: displayColor.a })
                            }
                            style={{
                                width: "100%",
                                boxShadow: "none",
                                borderRadius: "0.5rem",
                            }}
                        />
                    )}
                    {colorFormat === "HSL" && (
                        <HslColorPicker
                            color={rgbAToHslA(displayColor)}
                            onChange={(c) =>
                                setDisplayColor({
                                    ...hslAToRgbA({ ...c, a: 1 }),
                                    a: displayColor.a,
                                })
                            }
                            style={{
                                width: "100%",
                                boxShadow: "none",
                                borderRadius: "0.5rem",
                            }}
                        />
                    )}
                </div>

                <div className="flex gap-2 items-center mb-4">
                    <div className="relative flex-1">
                        <select
                            className="absolute inset-y-0 left-0 pl-3 pr-8 rounded-l-lg border-r border-gray-300 bg-gray-50 text-sm focus:outline-none"
                            value={colorFormat}
                            onChange={(e) =>
                                setColorFormat(
                                    e.target.value as "Hex" | "RGB" | "HSL"
                                )
                            }
                        >
                            <option value="Hex">Hex</option>
                            <option value="RGB">RGB</option>
                            <option value="HSL">HSL</option>
                        </select>
                        {/* Color Input based on format */}
                        <input
                            type="text"
                            value={
                                colorFormat === "Hex"
                                    ? rgbAToHex(displayColor)
                                    : colorFormat === "RGB"
                                    ? `rgba(${Math.round(
                                          displayColor.r
                                      )}, ${Math.round(
                                          displayColor.g
                                      )}, ${Math.round(
                                          displayColor.b
                                      )}, ${displayColor.a.toFixed(2)})`
                                    : `hsla(${Math.round(
                                          rgbAToHslA(displayColor).h
                                      )}, ${Math.round(
                                          rgbAToHslA(displayColor).s
                                      )}%, ${Math.round(
                                          rgbAToHslA(displayColor).l
                                      )}%, ${displayColor.a.toFixed(2)})`
                            }
                            onChange={(e) => {
                                const value = e.target.value;
                                let newRgba = { ...displayColor };
                                if (colorFormat === "Hex") {
                                    newRgba = hexToRgbA(value);
                                } else if (colorFormat === "RGB") {
                                    const rgbaMatch = value.match(
                                        /^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)$/
                                    );
                                    if (rgbaMatch) {
                                        newRgba = {
                                            r: parseInt(rgbaMatch[1], 10),
                                            g: parseInt(rgbaMatch[2], 10),
                                            b: parseInt(rgbaMatch[3], 10),
                                            a: rgbaMatch[4]
                                                ? parseFloat(rgbaMatch[4])
                                                : displayColor.a,
                                        };
                                    }
                                } else if (colorFormat === "HSL") {
                                    const hslaMatch = value.match(
                                        /^hsla?\((\d+),\s*(\d+)%,\s*(\d+)%(?:,\s*(\d*\.?\d+))?\)$/
                                    );
                                    if (hslaMatch) {
                                        newRgba = hslAToRgbA({
                                            h: parseInt(hslaMatch[1], 10),
                                            s: parseInt(hslaMatch[2], 10),
                                            l: parseInt(hslaMatch[3], 10),
                                            a: hslaMatch[4]
                                                ? parseFloat(hslaMatch[4])
                                                : displayColor.a,
                                        });
                                    }
                                }
                                setDisplayColor(newRgba);
                            }}
                            className="w-full pl-25 pr-1 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                    </div>
                    {/* Percentage Input for Alpha */}
                    <div className="flex items-center w-20">
                        <input
                            type="text"
                            value={`${Math.round(displayColor.a * 100)}%`}
                            onChange={(e) => {
                                const percentage = parseInt(
                                    e.target.value.replace("%", ""),
                                    10
                                );
                                const newAlpha = isNaN(percentage)
                                    ? displayColor.a
                                    : Math.max(
                                          0,
                                          Math.min(1, percentage / 100)
                                      );
                                setDisplayColor({
                                    ...displayColor,
                                    a: newAlpha,
                                });
                            }}
                            className="w-full px-3 py-2 border rounded-lg text-sm text-center focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                    </div>
                </div>

                <div className="flex justify-between items-center mb-2">
                    <div className="text-sm">Saved colors:</div>
                </div>

                <div className="grid grid-cols-5 gap-2">
                    {savedColors.map((savedColor, index) => (
                        <button
                            key={index}
                            className="w-6 h-6 rounded-full border cursor-pointer"
                            style={{ backgroundColor: savedColor }}
                            onClick={() => setColor(savedColor)}
                            tabIndex={0}
                            aria-label={`Select color ${savedColor}`}
                        />
                    ))}
                </div>
            </div>

            <div className="mb-4">
                <div className="text-sm mb-2">Quick Tools</div>
                    {/* Wrap the upload area with dropzone props */}
                <div
                    {...getRootProps()}
                    className={
                        `w-full p-3 border rounded-lg flex items-center justify-between gap-2 text-sm mb-2 cursor-pointer ` +
                        `${isDragActive ? 'border-purple-500 bg-purple-50' : 'border-gray-200 bg-white'}` // Add drag feedback styles
                    }
                >
                    {/* Apply input props */}
                    <input {...getInputProps()} />
                    <label
                        htmlFor="pantone-upload"
                        className="flex items-center gap-2 cursor-pointer"
                    >
                        <div className="w-5 h-5 bg-gray-200 rounded"></div>
                        <span>
                            {isDragActive
                                ? 'Drop the image here ...'
                                : 'Upload Pantone file or drag image here'} {/* Update label based on drag state */}
                        </span>
                    </label>
                    <button className="text-purple-600">Change</button>
                </div>
                <div className="w-full p-3 border rounded-lg flex items-center justify-start gap-2 text-sm">
                    {extractedColorInfo ? (
                        <div className="flex items-center gap-2">
                            <span>Hex Code:</span>
                            <span
                                className="font-semibold text-purple-700 cursor-pointer"
                                onClick={() => handleCopyToClipboard(extractedColorInfo)}
                                tabIndex={0} 
                                onKeyDown={(e) => { 
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        handleCopyToClipboard(extractedColorInfo);
                                    }
                                }}
                                aria-label={`Copy color value ${extractedColorInfo}`}
                            >
                                {extractedColorInfo}
                            </span>
                            {showCopiedMessage && (
                                <span className="text-xs text-green-600">Copied!</span>
                            )}
                        </div>
                    ) : (
                        <span>Color info will appear here</span>
                    )}
                </div>
            </div>

            <div className="flex gap-2 mt-4">
                <Button
                    variant="outline"
                    className="flex-1 bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-100"
                    onClick={resetCanvas}
                >
                    Reset
                </Button>
                <Button
                    className="flex-1 bg-brand hover:bg-brand/80 cursor-pointer"
                    onClick={() => applyFillColor(rgbAToHex(displayColor))}
                >
                    Apply
                </Button>
            </div>
        </div>
    );
}
