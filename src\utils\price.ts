export const calculateDiscount = (price: string, discountedPrice: string): number => {
    // Remove commas and convert to numbers
    const originalPrice = parseFloat(price.replace(/,/g, ''));
    const finalPrice = parseFloat(discountedPrice.replace(/,/g, ''));
    
    if (isNaN(originalPrice) || isNaN(finalPrice) || originalPrice <= 0 || originalPrice === finalPrice) {
        return 0;
    }
    
    return Math.round(((originalPrice - finalPrice) / originalPrice) * 100);
};

