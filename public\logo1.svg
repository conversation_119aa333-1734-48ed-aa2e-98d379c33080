<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="18" height="18" rx="3.375" fill="url(#paint0_linear_121_1353)"/>
<g filter="url(#filter0_dii_121_1353)">
<path d="M14.7005 4.51797L5.92051 7.29119C5.67836 7.36627 5.59991 7.6732 5.7841 7.85743L7.37022 9.44354L3.49193 12.9024C3.22243 13.1412 3.4578 13.5743 3.80231 13.4891L12.8279 11.2276C13.0837 11.1626 13.1758 10.8455 12.9882 10.6545L11.3134 8.98312L15.0519 5.07749C15.294 4.82157 15.0314 4.40889 14.7005 4.51797Z" fill="#FFD900"/>
</g>
<defs>
<filter id="filter0_dii_121_1353" x="1.125" y="2.25" width="16.271" height="13.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.125"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_121_1353"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_121_1353" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.28125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_121_1353"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.28125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_121_1353" result="effect3_innerShadow_121_1353"/>
</filter>
<linearGradient id="paint0_linear_121_1353" x1="0" y1="0" x2="18" y2="18" gradientUnits="userSpaceOnUse">
<stop stop-color="#6C3EDF"/>
<stop offset="1" stop-color="#5C0092"/>
</linearGradient>
</defs>
</svg>
