import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import { Chat } from "@/types/app.types";

interface TrendGptState {
  chats: Chat[];
  activeChat: string | null;
  isLoading: boolean;
  error: string | null;
  landingPageSeen: boolean;
}

const initialState: TrendGptState = {
  chats: [],
  activeChat: null,
  isLoading: false,
  error: null,
  landingPageSeen: false
}

const trendGptSlice = createSlice({
  name: "trendGpt",
  initialState,
  reducers: {
    fetchChats: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    fetchChatsSuccess: (state, action: PayloadAction<Chat[]>) => {
      state.chats = action.payload;
      state.isLoading = false;
    },
    fetchChatsError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    createChat: (state, action: PayloadAction<{ id: string; title: string }>) => {
      state.chats.push({
        id: action.payload.id,
        title: action.payload.title,
        messages: [],
        isGenerating: false,
      });
      state.activeChat = action.payload.id;
    },
    setActiveChat: (state, action: PayloadAction<string>) => {
      state.activeChat = action.payload;
    },
    addMessage: (
      state,
      action: PayloadAction<{
        chatId: string;
        message: {
          id: string;
          role: "user" | "assistant";
          content: string;
          status?: "loading" | "complete" | "error";
          citations?: string[];
          images?: string[];
        };
      }>
    ) => {
      const chat = state.chats.find((c) => c.id === action.payload.chatId);
      if (chat) {
        chat.messages.push(action.payload.message);
      }
    },
    setMessageStatus: (
      state,
      action: PayloadAction<{ chatId: string; messageId: string; status: "loading" | "complete" | "error" }>
    ) => {
      const chat = state.chats.find((c) => c.id === action.payload.chatId);
      if (chat) {
        const message = chat.messages.find((m) => m.id === action.payload.messageId);
        if (message) {
          message.status = action.payload.status;
        }
      }
    },
    setGeneratingStatus: (state, action: PayloadAction<{ chatId: string; isGenerating: boolean }>) => {
      const chat = state.chats.find((c) => c.id === action.payload.chatId);
      if (chat) {
        chat.isGenerating = action.payload.isGenerating;
      }
    },
    updateChatTitle: (state, action: PayloadAction<{ chatId: string; title: string }>) => {
      const chat = state.chats.find((c) => c.id === action.payload.chatId);
      if (chat) {
        chat.title = action.payload.title;
      }
    },
    setLandingPageSeen: (state, action: PayloadAction<boolean>) => {
      state.landingPageSeen = action.payload;
    },
    deleteChat: (state, action: PayloadAction<string>) => {
      state.chats = state.chats.filter(chat => chat.id !== action.payload);
      if (state.activeChat === action.payload) {
        state.activeChat = state.chats[0]?.id || null;
      }
    }
  }
});

export const {
  fetchChats,
  fetchChatsSuccess,
  fetchChatsError,
  createChat,
  setActiveChat,
  addMessage,
  setMessageStatus,
  setGeneratingStatus,
  updateChatTitle,
  setLandingPageSeen,
  deleteChat
} = trendGptSlice.actions;

export default trendGptSlice.reducer;