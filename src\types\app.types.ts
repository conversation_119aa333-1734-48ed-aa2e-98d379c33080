export type AppType =
  | "home"
  | "dashboard"
  | "start-from-scratch"
  | "lifestyle-photography"
  | "product-photography"
  | "prints-and-patterns"
  | "add-prints"
  | "sketch-to-render"
  | "technical-drawings"
  | "background-generator"
  | "avatar-to-photorealism"
  | "graphics-and-placement"
  | "pdp-assets"
  | "image-to-video"
  | "tech-pack"
  | "garment-alterations"
  | "garment-color"
  | "trendgpt"
  | "discover"
  | "favourites"
  | "curations"
  | "library"
  | "history"
  | "catalog"
  | "orders"
  | "storefront"
  | "feedback"
  | "edit"
  | "apply-print-or-fabric"
  | "infinite-colorways"
  | "trims-embellishments"
  | "apply-aesthetic"
  | "apply-unconventional"
  | "inspiration-mix"
  | null

  export interface Chat {
    id: string;
    title: string;
    messages: Message[];
    isGenerating?: boolean;
  }

export interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  status?: "loading" | "complete" | "error";
  citations?: unknown[];
  images?: string[];
}

export interface Tab {
  id: string
  type: AppType
  title: string
}


export interface AppState {
  activeApp: AppType
  activeTabId: string | null
  tabs: Tab[]
  generatedImages: {
    [tabId: string]: string[]
  }
  isLoading: {
    images: {
      [tabId: string]: boolean
    }
  }
  error: {
    images: string | null
  }
  toolConfigurations: {
    [tabId: string]: ToolConfigurationState
  }
}


export interface ToolConfigurationState {
  productImage: string;
  mixPercentage: number;
  fabricImage: string;
  description?: string;
  productDescription?: string;
  referenceImage?: string;
  referenceImageDescription?: string;
  guidanceScale?: number;
  steps?: number;
  seed?: string;
  presets?: string[];
  mode?: string;
  weight?: number;
  referenceType?: string;
}



export interface ProductData {
  product_id: number;
  similarity_index: string;
  image_url: string;
}

export interface TrendSearchResponse {
  status: string;
  data: ProductData[];
}
