import type React from "react"
import { AppType } from "@/types/app.types"
import { TrendGPT } from "@/screens/Dashboard/SidebarTools/trend-gpt"
import { AITools } from "@/screens/Dashboard/SidebarTools/AITools"
import { GenericPage } from "@/components/generic-page"
import { LifestylePhotographyTool } from "@/screens/Dashboard/MiniApps/create/lifestyle-photography"
import { SketchToRenderTool } from "@/screens/Dashboard/MiniApps/create/sketch-to-render"
import { DemoFeaturePage } from "@/components/demo-feature-page"
import images from "@/constants/images"
import { BackgroundGeneratorTool } from "@/screens/Dashboard/MiniApps/create/background-generator"
import { Home } from "@/screens/Dashboard/SidebarTools/Home"
import { ProductPhotoTool } from "@/screens/Dashboard/MiniApps/create/product-photo"
import { AvatarToPhotorealismTool } from "@/screens/Dashboard/MiniApps/create/avatar-to-photorealism"
import { PrintsAndPatternsTool } from "@/screens/Dashboard/MiniApps/create/prints-and-patterns"
import { TechnicalDrawingsTool } from "@/screens/Dashboard/MiniApps/create/technical-drawings"
import {Discover} from "@/screens/Dashboard/SidebarTools/Discover/Discover"
import { StartFromScratchTool } from "@/screens/Dashboard/MiniApps/create/start-from-scratch"
import { GraphicsAndPlacementTool } from "@/screens/Dashboard/MiniApps/create/graphics-and-placement"
import { ApplyPrintOrFabricTool } from "@/screens/Dashboard/MiniApps/mix/apply-print-or-fabric"
import { EditTool } from "@/screens/Dashboard/Edit/EditTool"
export interface AppConfig {
  id: AppType
  title: string
  showTabs: boolean
  component: React.ComponentType
  showCanvas?: boolean
  icon?: string
}

// Populating APP_CONFIG with actual application configurations
export const APP_CONFIG: Record<string, AppConfig> = {
  home: {
    id: "home",
    title: "Home",
    showTabs: false,
    showCanvas: false,
    component: Home,
    icon: images.dashboard
  },
  dashboard: {
    id: "dashboard",
    title: "AI Tools",
    showTabs: true,
    showCanvas: false,
    component: AITools,
    icon: images.dashboard
  },
  "lifestyle-photography": {
    id: "lifestyle-photography",
    title: "Lifestyle Photography",
    showTabs: true,
    showCanvas: true,
    component: LifestylePhotographyTool,
    icon: images.lifestylePhotographyImage
  },
  "product-photography": {
    id: "product-photography",
    title: "Product Photography",
    showTabs: true,
    showCanvas: true,
    component: ProductPhotoTool,
    icon: images.productPhotographyImage
  },
  "sketch-to-render": {
    id: "sketch-to-render",
    title: "Sketch to Render",
    showTabs: true,
    showCanvas: true,
    component: SketchToRenderTool,
    icon: images.sketchToRenderImage
  },
  "prints-and-patterns": {
    id: "prints-and-patterns",
    title: "Prints and Patterns",
    showTabs: true,
    showCanvas: true,
    component: PrintsAndPatternsTool,
    icon: images.pdpAssetsImage
  },
  "technical-drawings": {
    id: "technical-drawings",
    title: "Technical Drawings",
    showTabs: true,
    showCanvas: true,
    component: TechnicalDrawingsTool,
    icon: images.technicalDrawingsImage
  },
  "background-generator": {
    id: "background-generator",
    title: "Background Generator",
    showTabs: true,
    showCanvas: true,
    component: BackgroundGeneratorTool,
    icon: images.backgroundGeneratorImage
  },
  "avatar-to-photorealism": {
    id: "avatar-to-photorealism",
    title: "3D Avatar to Photorealism",
    showTabs: true,
    showCanvas: true,
    component: AvatarToPhotorealismTool,
    icon: images.AvatarToPhotorealismImage
  },
  "graphics-and-placement": {
    id: "graphics-and-placement",
    title: "Graphics and Placement Prints",
    showTabs: true,
    showCanvas: true,
    component: GraphicsAndPlacementTool,
    icon: images.graphicsAndPlacement
  },
  "pdp-assets": {
    id: "pdp-assets",
    title: "Make PDP Assets",
    showTabs: true,
    showCanvas: true,
    component: DemoFeaturePage,
  },
  "image-to-video": {
    id: "image-to-video",
    title: "Image to Video",
    showTabs: true,
    showCanvas: true,
    component: DemoFeaturePage,
  },
  "tech-pack": {
    id: "tech-pack",
    title: "Create TechPack",
    showTabs: true,
    showCanvas: true,
    component: DemoFeaturePage,
  },
  "garment-alterations": {
    id: "garment-alterations",
    title: "Make Garment Alterations",
    showTabs: true,
    showCanvas: true,
    component: DemoFeaturePage,
  },
  "garment-color": {
    id: "garment-color",
    title: "Change Garment Color",
    showTabs: true,
    showCanvas: true,
    component: DemoFeaturePage,
  },
  trendgpt: {
    id: "trendgpt",
    title: "TrendGPT",
    showTabs: false,
    showCanvas: true,
    component: TrendGPT,
  },
  discover: {
    id: "discover",
    title: "Discover",
    showTabs: false,
    showCanvas: true,
    component: Discover,
  },
  "start-from-scratch": {
    id: "start-from-scratch",
    title: "Start from Scratch",
    showTabs: true,
    showCanvas: true,
    component: StartFromScratchTool,
    icon: images.startFromScratch
  },
  "apply-print-or-fabric": {
    id: "apply-print-or-fabric",
    title: "Apply Print or Fabric",
    showTabs: true,
    showCanvas: true,
    icon: images.patternImage,
    component: ApplyPrintOrFabricTool,
  },
  favourites: {
    id: "favourites",
    title: "Favourites",
    showTabs: false,
    showCanvas: true,
    component: GenericPage,
  },
  curations: {
    id: "curations",
    title: "Curations & Reports",
    showTabs: false,
    showCanvas: true,
    component: GenericPage,
  },
  library: {
    id: "library",
    title: "My Library",
    showTabs: false,
    showCanvas: true,
    component: GenericPage,
  },
  history: {
    id: "history",
    title: "History",
    showTabs: false,
    showCanvas: true,
    component: GenericPage,
  },
  edit: {
    id: "edit",
    title: "Edit",
    showTabs: true,
    showCanvas: true,
    component: EditTool,
  }
}
