import React, { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { MagicSubMode, UseCase } from "../EditTool";
import { SAMState } from "@/types/sam";

interface MagicEditProps {
    imageEditor: any;
    prompt: string;
    setPrompt: (prompt: string) => void;
    generateMaskAndSend: (prompt: string, mode: MagicSubMode, useCase: UseCase) => Promise<void>;
    generateRemoveBackground: () => Promise<void>;
    magicSubMode: MagicSubMode;
    setMagicSubMode: (mode: MagicSubMode) => void;
    testLog?: () => Promise<void>;
    samState: SAMState;
    isSmartModeActive: boolean;
}

export function MagicEdit({
    imageEditor,
    prompt,
    setPrompt,
    generateMaskAndSend,
    generateRemoveBackground,
    magicSubMode,
    setMagicSubMode,
    testLog,
    samState,
    isSmartModeActive,
}: MagicEditProps) {
    useEffect(() => {
        if (!imageEditor.canvas) return;
        if (magicSubMode === "brush") {
            imageEditor.canvas.isDrawingMode = true;
        } else {
            imageEditor.canvas.isDrawingMode = false;
        }

        return () => {
            if (imageEditor.canvas) {
                imageEditor.canvas.isDrawingMode = false;
            }
        };
    }, [imageEditor.canvas, magicSubMode]);

    const handleBrushApply = () => {
        if (magicSubMode === "brush" && imageEditor.hasPainted) {
            generateMaskAndSend(prompt, magicSubMode, "insert");
        }
    };

    const handleBrushErase = () => {
        if (magicSubMode === "brush" && imageEditor.hasPainted) {
            generateMaskAndSend("", magicSubMode, "erase");
        }
    };

    const isBrushActionsEnabled = magicSubMode === "brush" && imageEditor.hasPainted;

    return (
        <div className="space-y-4">
            <div className="p-3 bg-purple-50 rounded-lg border border-purple-100">
                <div className="flex flex-col space-y-2 items-center">
                    <p className="text-xs text-gray-500 mb-1">
                        Choose selection method:
                    </p>
                    <div className="grid grid-cols-2 gap-2 w-full">
                        <Button
                            variant={magicSubMode === "smart" ? "default" : "outline"}
                            onClick={() => setMagicSubMode("smart")}
                            className={`w-full ${magicSubMode === "smart" ? "bg-purple-600 hover:bg-purple-700 text-white" : "hover:border-purple-400"}`}
                            size="sm"
                        >
                            Smart Select
                        </Button>
                        <Button
                            variant={magicSubMode === "brush" ? "default" : "outline"}
                            onClick={() => setMagicSubMode("brush")}
                            className={`w-full ${magicSubMode === "brush" ? "bg-purple-600 hover:bg-purple-700 text-white" : "hover:border-purple-400"}`}
                            size="sm"
                        >
                            Brush Select
                        </Button>
                    </div>
                    {testLog && (
                         <Button variant="link" size="sm" className="text-xs text-purple-600" onClick={testLog}>Developer Test Log</Button>
                    )}
                </div>
            </div>

            {isSmartModeActive && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-100 space-y-2">
                    <p className="text-sm font-semibold text-blue-700">Smart Select Mode Active</p>
                    <p className="text-xs text-gray-600">
                        Click on the image to make selections. Use the toolbar below the canvas to manage your selection (undo, redo, reset, invert, add/remove points, create cutout).
                    </p>
                     {samState.isLoading && (
                        <p className="text-xs text-blue-500 flex items-center"><svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>SAM Processing...</p>
                    )}
                    {samState.error && (
                        <p className="text-xs text-red-500">Error: {samState.error}</p>
                    )}
                    {samState.clicks && samState.clicks.length > 0 && (
                        <p className="text-xs text-gray-500">
                            Current selection: {samState.clicks.length} point(s).
                            {samState.maskImg && " Mask preview active."}
                        </p>
                    )}
                    {(!samState.tensor && !samState.isLoading && !samState.error && imageEditor.hasImage) && (
                        <p className="text-xs text-orange-500">Initializing Smart Select for the image...</p>
                    )}
                </div>
            )}

            <div className="space-y-3">
                <div>
                    <Label htmlFor="prompt-magic-edit" className="text-sm font-medium text-gray-700">
                        Describe your edit (for generative fill)
                    </Label>
                    <textarea
                        id="prompt-magic-edit"
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="w-full h-20 p-2 mt-1 text-sm border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent shadow-sm"
                        placeholder="e.g., 'a field of flowers', 'make it a sunny day'"
                    />
                </div>

                {magicSubMode === "brush" && (
                    <div className="space-y-2 pt-2 border-t border-gray-200">
                         <p className="text-xs text-gray-500">
                            Use brush to mark area, then apply generative edit.
                        </p>
                        <div className="flex space-x-2">
                            <Button
                                className={`flex-1 ${isBrushActionsEnabled ? "bg-purple-600 hover:bg-purple-700 text-white" : "bg-gray-300 text-gray-500 cursor-not-allowed"}`}
                                onClick={handleBrushApply}
                                disabled={!isBrushActionsEnabled}
                                size="sm"
                            >
                                Apply with Brush
                            </Button>
                            <Button 
                                variant="outline" 
                                className="flex-1 hover:border-red-400 hover:text-red-500"
                                onClick={handleBrushErase}
                                disabled={!isBrushActionsEnabled}
                                size="sm"
                            >
                                Erase with Brush
                            </Button>
                        </div>
                    </div>
                )}

                <div className="pt-3 border-t border-gray-200 space-y-2">
                     <Label className="text-sm font-medium text-gray-700">
                        Quick Actions
                    </Label>
                    <Button
                        variant="outline" className="w-full justify-start text-gray-700 hover:border-purple-400 hover:text-purple-600"
                        onClick={generateRemoveBackground}
                        disabled={!imageEditor.hasImage}
                    >
                        Remove Background
                    </Button>
                </div>
            </div>
        </div>
    );
}
