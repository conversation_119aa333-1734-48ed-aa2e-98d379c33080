import { useState, useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { v4 as uuidv4 } from "uuid"
import { Search, PaperclipIcon, Send, Copy, ThumbsDown, Pencil, Trash2, ChevronDown } from "lucide-react"
import { toast } from "sonner"
import { DeleteConfirmationDialog } from "@/components/delete-confirmation-dialog"
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { queryTrendGPT } from '@/lib/api';

import type { RootState } from "@/types/store.types"
import { 
  createChat, 
  setActiveChat, 
  addMessage, 
  setGeneratingStatus, 
  updateChatTitle, 
  deleteChat 
} from "@/lib/store/trendGptSlice"
import { cn } from "@/lib/utils"
import { images } from "@/constants"



// Define the form schema
const messageSchema = z.object({
  message: z.string().min(1, "Message cannot be empty"),
});

type MessageFormValues = z.infer<typeof messageSchema>;


const CitationsList = ({ citations }: { citations: string[] }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 text-sm text-[#535862] hover:text-[#6938ef] transition-colors"
      >
        <ChevronDown
          size={16}
          className={`transition-transform ${isExpanded ? "rotate-180" : ""}`}
        />
        <span>Citations ({citations.length})</span>
      </button>
      
      {isExpanded && (
        <div className="mt-2 space-y-2">
          {citations.map((citation, index) => (
            <div 
              key={index} 
              className="flex gap-2 text-sm group relative"
              title={citation} // Shows the full URL on hover
            >
              <span className="text-[#717680]">[[{index + 1}]]</span>
              <a
                href={citation}
                target="_blank"
                rel="noopener noreferrer"
                className="text-brand hover:text-brand/80 truncate max-w-[500px]"
              >
                {citation}
              </a>
              <div className="absolute invisible group-hover:visible bg-black text-white p-2 rounded text-xs -top-8 left-0 whitespace-nowrap">
                {citation}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const MessageContent = ({ content, citations, images }: { 
  content: string | object; 
  citations?: string[]; 
  images?: string[] 
}) => {
  // Custom renderer for transforming citation numbers into links
  const transformCitationLinks = (text: string) => {
    return text.replace(/\[(\d+)\]/g, (match, num) => {
      const index = parseInt(num) - 1;
      if (citations && citations[index]) {
        // Keep the square brackets in the display text
        return `[[${num}]](${citations[index]})`;
      }
      return match;
    });
  };

  // Ensure content is a string and transform citation links
  const contentString = typeof content === 'object' 
    ? transformCitationLinks(JSON.stringify(content))
    : transformCitationLinks(String(content));

  return (
    <div className="flex flex-col gap-4">
      {/* Images Section */}
      {images && images.length > 0 && (
        <div className="flex flex-row gap-2 overflow-x-auto pb-2">
          {images.map((image, index) => (
            <div key={index} className="flex-shrink-0">
              <img
                src={image}
                alt={`Generated image ${index + 1}`}
                className="w-[200px] h-[200px] object-cover rounded-lg"
              />
            </div>
          ))}
        </div>
      )}

      {/* Text Content Section */}
      <div className="prose prose-sm max-w-none prose-table:table-auto prose-headings:mb-2 prose-headings:mt-4 prose-p:my-2 prose-li:my-0">
        <ReactMarkdown 
          remarkPlugins={[remarkGfm]}
          components={{
            a: ({ children, ...props }) => (
              <a 
                {...props} 
                className="text-brand hover:text-brand/80 no-underline" 
                target="_blank" 
                rel="noopener noreferrer"
              >
                {children}
              </a>
            ),
          }}
        >
          {contentString}
        </ReactMarkdown>
      </div>

      {/* Citations Section */}
      {citations && citations.length > 0 && (
        <div className="mt-4 border-t border-[#e2e8f0] pt-2">
          <CitationsList citations={citations} />
        </div>
      )}
    </div>
  );
};

export function TrendGPT() {
  const dispatch = useDispatch()
  const { chats, activeChat } = useSelector((state: RootState) => state.trendGpt)
  const [showLanding, setShowLanding] = useState(false)
  const [editingChatId, setEditingChatId] = useState<string | null>(null)
  const [editTitle, setEditTitle] = useState("")
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [chatToDelete, setChatToDelete] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm<MessageFormValues>({
    resolver: zodResolver(messageSchema),
    defaultValues: {
      message: "",
    },
  });
  
  const { user } = useSelector((state: RootState) => state.auth)


  const currentChat = chats.find((chat) => chat.id === activeChat);

  useEffect(() => {
    if (activeChat && currentChat?.messages.length > 0) {
      setShowLanding(false)
    }
  }, [chats, activeChat, currentChat])
  const handleNewChat = () => {
    const id = uuidv4()
    dispatch(createChat({ id, title: "New Chat" }))
    dispatch(setActiveChat(id))
    setShowLanding(false)
  }

  const handleChatSelect = (chatId: string) => {
    dispatch(setActiveChat(chatId))
    setShowLanding(false)
  }

  const onSubmit = async (data: MessageFormValues) => {
    if (!data.message.trim()) return;

    let chatId = activeChat;
    if (!chatId) {
      chatId = uuidv4();
      const newTitle = data.message.split(" ").slice(0, 3).join(" ") + "...";
      dispatch(createChat({ id: chatId, title: newTitle }));
      dispatch(setActiveChat(chatId));
    } else {
      const chat = chats.find((c) => c.id === chatId);
      if (chat && chat.messages.length === 0) {
        const newTitle = data.message.split(" ").slice(0, 3).join(" ") + "...";
        dispatch(updateChatTitle({ chatId, title: newTitle }));
      }
    }

    const messageId = uuidv4();
    const userMessage = data.message;
    
    // Clear the form
    reset();

    // Add user message to UI
    dispatch(
      addMessage({
        chatId,
        message: {
          id: messageId,
          role: "user",
          content: userMessage,
        },
      })
    );

    dispatch(setGeneratingStatus({ chatId, isGenerating: true }));

    try {
      const currentChat = chats.find(chat => chat.id === chatId);
      // Format history in the correct structure for the API
      const history = currentChat?.messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })) || [];

      const response = await queryTrendGPT(userMessage, history);
      
      dispatch(
        addMessage({
          chatId,
          message: {
            id: uuidv4(),
            role: "assistant",
            content: response.content,
            citations: response.citations,
            images: response.images
          },
        })
      );
    } catch (error) {
      console.error('Error:', error);
      // Add a more specific error message
      dispatch(
        addMessage({
          chatId,
          message: {
            id: uuidv4(),
            role: "assistant",
            content: "I encountered an error while processing your request. This might be due to a connection issue or server problem. Please try again.",
          },
        })
      );
      toast.error("Failed to get response from TrendGPT");
    } finally {
      dispatch(setGeneratingStatus({ chatId, isGenerating: false }));
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setValue("message", suggestion, { 
      shouldValidate: true 
    });
  };

  const handleEditClick = (chatId: string, currentTitle: string) => {
    setEditingChatId(chatId)
    setEditTitle(currentTitle)
  }

  const handleEditSubmit = (chatId: string) => {
    if (editTitle.trim()) {
      dispatch(updateChatTitle({ chatId, title: editTitle.trim() }))
      toast.success("Chat name updated successfully")
    }
    setEditingChatId(null)
    setEditTitle("")
  }

  const handleDeleteConfirm = () => {
    if (chatToDelete) {
      dispatch(deleteChat(chatToDelete))
      toast.success("Chat deleted successfully")
      setDeleteDialogOpen(false)
      setChatToDelete(null)
    }
  }

  const openDeleteDialog = (chatId: string) => {
    setChatToDelete(chatId)
    setDeleteDialogOpen(true)
  }

  return (
    <>
      <div className="flex h-[calc(100vh-64px)] flex-col md:flex-row">
        {/* Chat list sidebar */}
        <div className="w-full md:w-[300px] border-r border-[#e2e8f0] flex flex-col md:flex-col md:h-full"> 
          <div className="p-3 space-y-3"> 
            <div className="flex items-center gap-2 mb-3"> 
              <h2 className="text-lg font-semibold"> 
                Trend<span className="text-brand">GPT</span>
              </h2>
            </div>

            <button
              className="w-full py-2 bg-brand text-white font-medium rounded-lg hover:bg-brand cursor-pointer transition-colors" 
              onClick={handleNewChat}
            >
              New Chat
            </button>

            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-[#717680]" />
              <input
                type="text"
                placeholder="Search"
                className="w-full pl-9 pr-4 py-2 rounded-lg border border-[#e2e8f0] focus:outline-none focus:ring-2 focus:ring-brand focus:border-transparent" 
              />
            </div>
          </div>

          <div className="p-3 border-b border-[#e2e8f0]"> 
            <h3 className="font-medium text-[#414651]">Chats</h3>
          </div>

          <div className="flex-1 overflow-auto">
            {chats.length > 0 ? (
              chats.map((chat) => (
                <div
                  key={chat.id}
                  className={cn(
                    "px-3 py-2 cursor-pointer hover:bg-[#f8fafc] flex items-center justify-between", 
                    chat.id === activeChat ? "bg-[#f4f3ff] text-brand" : "text-[#535862]",
                  )}
                >
                  <div className="flex-1" onClick={() => handleChatSelect(chat.id)}>
                    {editingChatId === chat.id ? (
                      <input
                        type="text"
                        value={editTitle}
                        onChange={(e) => setEditTitle(e.target.value)}
                        onBlur={() => handleEditSubmit(chat.id)}
                        onKeyDown={(e) => {
                          if (e.key === "Enter") {
                            handleEditSubmit(chat.id)
                          }
                        }}
                        className="w-full px-2 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-brand"
                        autoFocus
                      />
                    ) : (
                      chat.title
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      className="p-1 hover:bg-[#e2e8f0] rounded"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleEditClick(chat.id, chat.title)
                      }}
                    >
                      <Pencil size={16} />
                    </button>
                    <button
                      className="p-1 hover:bg-[#e2e8f0] rounded text-red-500"
                      onClick={(e) => {
                        e.stopPropagation()
                        openDeleteDialog(chat.id)
                      }}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="px-4 py-3 text-[#717680]">No chats yet</div>
            )}
          </div>
        </div>

        {/* Chat area */}
        <div className="flex-1 flex flex-col">
          {activeChat && currentChat && !showLanding ? (
            <>
              <div className="flex-1 overflow-auto p-3"> 
                {currentChat.messages.length > 0 ? (
                  currentChat.messages.map((message) => (
                    <div key={message.id} className="mb-4"> 
                      <div className="flex items-center gap-2 mb-2">
                        <div
                          className={cn(
                            "w-7 h-7 rounded-full flex items-center justify-center",
                            message.role === "user" ? "bg-green-500" : "bg-brand",
                          )}
                        >
                          {message.role === "user" ? (
                            <span className="text-white font-medium">A</span>
                          ) : (
                            <img
                              src={images.logo1}
                              alt="TrendGPT"
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>
                        <div className="font-medium">{message.role === "user" ? "You" : "TrendGPT"}</div>
                      </div>
                      <div className="pl-10">
                        <div className="text-[#414651] text-sm">
                          <MessageContent content={message.content} citations={message.citations as string[]} images={message.images as string[]} />
                        </div>
                        {message.role === "assistant" && (
                          <div className="flex items-center gap-3 mt-3 text-[#717680]">
                            <button 
                              className="p-1 hover:bg-[#f8fafc] rounded cursor-pointer"
                              onClick={() => {
                                navigator.clipboard.writeText(message.content);
                                toast.success("Copied to clipboard");
                              }}
                            >
                              <Copy size={16} />
                            </button>
                            <button className="p-1 hover:bg-[#f8fafc] rounded cursor-pointer">
                              <ThumbsDown size={16} />
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="flex-1 flex items-center justify-center text-[#717680]">
                    <p>Start a new conversation</p>
                  </div>
                )}

                {currentChat.isGenerating && <div className="pl-10 text-[#717680]">TrendGPT is thinking...</div>}
              </div>

              <div className="p-3 border-t border-[#e2e8f0]"> 
                <div className="relative">
                  <input
                    type="text"
                    placeholder={currentChat?.messages.length > 0 ? "Ask follow up" : "Ask me anything about fashion..."}
                    className={cn(
                      "w-full pl-4 pr-12 py-2 rounded-lg border border-[#e2e8f0] focus:outline-none focus:ring-2 focus:ring-brand focus:border-transparent",
                      errors.message && "border-red-500"
                    )}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault();
                        handleSubmit(onSubmit)();
                      }
                    }}
                    {...register("message")}
                  />
                  {errors.message && (
                    <p className="text-red-500 text-xs mt-1">{errors.message.message}</p>
                  )}
                  <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-2">
                    {/* <button className="text-[#717680] hover:text-[#535862] cursor-pointer">
                      <PaperclipIcon size={18} />
                    </button> */}
                    <button className="text-brand cursor-pointer hover:text-brand/80" onClick={handleSubmit(onSubmit)}>
                      <Send size={18} />
                    </button>
                  </div>
                </div>
                <div className="text-xs text-[#717680]  text-center">
                  TrendGPT can make mistakes. Consider checking important information.
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex flex-col items-center justify-center h-full">
              <div className="mb-8"> 
                <img src={images.trendGptImage} alt="TrendGPT Logo" className="w-50 h-50" />
              </div>
              <h1 className="text-2xl font-bold mb-3 text-center">Welcome to TrendGPT, {user?.full_name || 'User'}!</h1> 
              <p className="text-center text-[#717680] max-w-md mb-6"> 
                Mix and match styles, generate better copy, and explore ideas faster than ever with your personal fashion
                expert
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-8 w-full max-w-3xl"> 
                <button
                  type="button"
                  className="p-2 border border-[#e2e8f0] rounded-lg text-left hover:border-brand transition-colors cursor-pointer" 
                  onClick={() => handleSuggestionClick("Breezy travel looks for tropical beach vacations")}
                >
                  Breezy travel looks for tropical beach vacations
                </button>
                <button
                  type="button"
                  className="p-2 border border-[#e2e8f0] rounded-lg text-left hover:border-brand transition-colors cursor-pointer" 
                  onClick={() => handleSuggestionClick("Monsoon-friendly street style outfit inspiration")}
                >
                  Monsoon-friendly street style outfit inspiration
                </button>
                <button
                  type="button"
                  className="p-2 border border-[#e2e8f0] rounded-lg text-left hover:border-brand transition-colors cursor-pointer" 
                  onClick={() => handleSuggestionClick("Layering ideas for light winter days in India")}
                >
                  Layering ideas for light winter days in India
                </button>
              </div>

              <div className="relative w-full max-w-2xl px-4 mt-8"> 
                <form onSubmit={handleSubmit(onSubmit)}>
                  <input
                    type="text"
                    placeholder="Can you recommend an outfit for a summer wedding in India?"
                    className={cn(
                      "w-full pl-10 pr-12 py-2 rounded-lg border border-[#e2e8f0] focus:outline-none focus:ring-2 focus:ring-brand focus:border-transparent", 
                      errors.message && "border-red-500"
                    )}
                    {...register("message")}
                  />
                  {errors.message && (
                    <p className="text-red-500 text-xs mt-1">{errors.message.message}</p>
                  )}
                  <div className="absolute left-6 top-1/2 -translate-y-1/2">
                    <PaperclipIcon size={18} className="text-[#717680]" />
                  </div>
                  <button
                    type="submit"
                    className="absolute right-6 top-1/2 -translate-y-1/2 bg-brand text-white rounded-full p-1 cursor-pointer"
                  >
                    <Send size={18} />
                  </button>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Chat"
        description="Are you sure you want to delete this chat? This action cannot be undone."
      />
    </>
  )
}
