import { RegisterForm } from "@/components/RegisterForm";
import AuthLayout from "./AuthLayout";
import { useSelector } from "react-redux";
import { Navigate } from "react-router-dom";
import { RootState } from "@/types/store.types";

export default function Register() {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  // Redirect to dashboard if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <AuthLayout>
      <RegisterForm />
    </AuthLayout>
  );
}
