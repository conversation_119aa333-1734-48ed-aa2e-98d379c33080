import { Card, CardContent } from "@/components/ui/card"
import { images } from "@/constants"

export const EntrepreneurSection = () => {
  const cardData = [
    {
      id: 1,
      icon: images.individual,
      title: "For Entrepreneurs",
      description: "Jumpstart fashion ideas with AI-generated designs and ready-to-go suppliers.",
    },
    {
      id: 2,
      icon: images.organisation,
      title: "For Growing Brands",
      description: "Scale your design pipeline and seamlessly streamline production like a pro.",
    },
    {
      id: 3,
      icon: images.enterprise,
      title: "For Enterprises",
      description: "Supercharge teams with faster concepting, sourcing, and market launches.",
    },
  ]

  return (
    <section className="relative w-full px-6 md:px-8 py-16 md:py-24 bg-[#321d67]">
      <div className="container mx-auto max-w-[1440px]">
        <h2 className="mb-8 md:mb-10 [font-family:'Plus_Jakarta_Sans',Helvetica] font-bold text-white text-3xl md:text-5xl tracking-[-0.96px] leading-[normal]">
          For everyone, from entrepreneurs to enterprises
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {cardData.map((card) => (
            <Card key={card.id} className="bg-[#45298c80] border-none rounded-[20px] overflow-hidden">
              <CardContent className="flex flex-col items-start gap-4 pt-1 px-6 pb-6">
                <div className="relative w-[50px] h-[50px] bg-[#321d67] rounded-lg overflow-hidden">
                  <img className="absolute w-13 h-13" alt="Icon" src={card.icon || "/placeholder.svg"} />
                </div>

                <div className="flex flex-col items-start gap-2 w-full">
                  <h3 className="self-stretch [font-family:'Plus_Jakarta_Sans',Helvetica] font-bold text-white text-xl md:text-2xl tracking-[0] leading-9">
                    {card.title}
                  </h3>
                  <p className="text-[#ffffffb2] text-base md:text-lg self-stretch [font-family:'Inter',Helvetica] font-normal tracking-[0] leading-[26px]">
                    {card.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
