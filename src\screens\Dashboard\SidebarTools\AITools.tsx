import { useDispatch, useSelector } from "react-redux"
import { v4 as uuidv4 } from "uuid"
import { Search } from "lucide-react"
import { addTab, updateTab, updateToolConfiguration, startGeneratingImages, setGeneratedImages, setImagesError } from "@/lib/store/appSlice"
import { setSidebarCollapsed } from "@/lib/store/sidebarSlice"
import type { RootState } from "@/types/store.types"
import { AppType } from "@/types/app.types"
import { images } from "@/constants"
import { motion } from "framer-motion"
import { captureEvent } from "@/utils/postHogClient"
import { useState } from "react"
import { generateStartFromScratch } from "@/lib/api"
import { toast } from "sonner"

export function AITools() {
  const dispatch = useDispatch()
  const { activeTabId } = useSelector((state: RootState) => state.app)
  const [searchPrompt, setSearchPrompt] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)

  const handleFeatureClick = (type: AppType, title: string) => {
    captureEvent("mini_app_clicked", {"mini_app_name": title});
    if (activeTabId) {
      dispatch(
        updateTab({
          id: activeTabId,
          type,
          title,
        })
      )
    } else {
      dispatch(
        addTab({
          id: uuidv4(),
          type,
          title,
        })
      )
    }

    if (type !== "dashboard") {
      dispatch(setSidebarCollapsed(true))
    }
  }

  const handleGenerateClick = async () => {
    if (!searchPrompt.trim() || isGenerating) return;
    
    captureEvent("search_generate_clicked", {"prompt": searchPrompt});
    setIsGenerating(true);
    
    if (activeTabId) {
      dispatch(
        updateTab({
          id: activeTabId,
          type: "start-from-scratch" as AppType,
          title: "Start from scratch",
        })
      );
      
      // Update the tool configuration 
      dispatch(
        updateToolConfiguration({
          tabId: activeTabId,
          values: {
            description: searchPrompt,
            referenceImage: "",
            guidanceScale: 7.5,
            steps: 30,
            seed: "0",
          }
        })
      );
      
      // Collapse sidebar to show the updated tab
      dispatch(setSidebarCollapsed(true));
      
      dispatch(startGeneratingImages(activeTabId));
      
      try {
        const temperatures = [0.25, 0.5, 0.75, 1];
        const imagePromises = temperatures.map(temp => 
          generateStartFromScratch(searchPrompt, "", temp)
        );
        
        const generatedUrls = await Promise.all(imagePromises);
        
        dispatch(
          setGeneratedImages({
            tabId: activeTabId,
            images: generatedUrls
          })
        );
        
        console.log('Successfully generated 4 images');
      } catch (error) {
        console.error('Image generation failed:', error);
        dispatch(setImagesError('Failed to generate images. Please try again.'));
        toast.error('Failed to generate images. Please try again.');
      } finally {
        setIsGenerating(false);
      }
    } else {
      // If there's no active tab, create a new one (fallback)
      const newTabId = uuidv4();
      
      dispatch(
        addTab({
          id: newTabId,
          type: "start-from-scratch" as AppType,
          title: "Start from scratch",
        })
      );
      
      // Rest of the code for new tab creation...
      // (Similar to above, but using newTabId)
      
      // Update the tool configuration with the prompt
      dispatch(
        updateToolConfiguration({
          tabId: newTabId,
          values: {
            description: searchPrompt,
            referenceImage: "",
            guidanceScale: 7.5, // default value
            steps: 30, // default value
            seed: "0",
          }
        })
      );
      
      dispatch(setSidebarCollapsed(true));
      dispatch(startGeneratingImages(newTabId));
      
      try {
        const temperatures = [0.25, 0.5, 0.75, 1];
        const imagePromises = temperatures.map(temp => 
          generateStartFromScratch(searchPrompt, "", temp)
        );
        
        const generatedUrls = await Promise.all(imagePromises);
        
        dispatch(
          setGeneratedImages({
            tabId: newTabId,
            images: generatedUrls
          })
        );
      } catch (error) {
        console.error('Image generation failed:', error);
        dispatch(setImagesError('Failed to generate images. Please try again.'));
        toast.error('Failed to generate images. Please try again.');
      } finally {
        setIsGenerating(false);
      }
    }
  }

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="p-3 md:p-4 max-w-[900px] mx-auto h-[calc(100vh-48px)] overflow-auto custom-scrollbar w-full"
    >
      {/* CREATE Section */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-4">
          <img src={images.createIcon} width={30} height={30} alt="" className="text-brand" />
          <h2 className="text-brand font-bold ml-2 text-base">CREATE</h2>
        </div>

        {/* Search Bar */}
        <div className="flex flex-col md:flex-row gap-2 mb-6">
          <div className="flex-1 relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2">
              <Search className="w-4 h-4 text-[#717680]" />
            </div>
            <input
              type="text"
              placeholder="mid length shirt dress with shirt collar, long sleeves..."
              className="w-full h-10 pl-8 pr-3 text-sm rounded-lg border border-[#e2e8f0] focus:outline-none focus:ring-2 focus:ring-brand focus:border-transparent"
              value={searchPrompt}
              onChange={(e) => setSearchPrompt(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !isGenerating) {
                  handleGenerateClick();
                }
              }}
              disabled={isGenerating}
            />
          </div>
          <button 
            className={`h-10 px-4 text-sm bg-brand text-white cursor-pointer font-medium rounded-lg ${isGenerating ? 'opacity-70 cursor-not-allowed' : 'hover:bg-brand/90'} transition-colors`}
            onClick={handleGenerateClick}
            disabled={isGenerating}
          >
            {isGenerating ? "Generating..." : "Generate"}
          </button>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {createFeatures.map((feature) => (
            <FeatureCard
              key={feature.title}
              title={feature.title}
              description={feature.description}
              imageSrc={feature.imageSrc}
              onClick={() => handleFeatureClick(feature.type, feature.title)}
            />
          ))}
        </div>
      </div>

      {/* EDIT Section */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-4">
          <img src={images.editIcon} width={30} height={30} alt="" className="text-brand" />
          <h2 className="text-brand font-bold ml-2 text-base">EDIT</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {editFeatures.map((feature) => (
            <FeatureCard
              key={feature.title}
              title={feature.title}
              description={feature.description}
              imageSrc={feature.imageSrc}
              onClick={() => handleFeatureClick(feature.type, feature.title)}
            />
          ))}
        </div>
      </div>

      {/* MIX Section */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-4">
          <img src={images.mixIcon} width={30} height={30} alt="" className="text-brand" />
          <h2 className="text-brand font-bold ml-2 text-base">MIX</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {mixFeatures.map((feature) => (
            <FeatureCard
              key={feature.title}
              title={feature.title}
              description={feature.description}
              imageSrc={feature.imageSrc}
              onClick={() => handleFeatureClick(feature.type, feature.title)}
            />
          ))}
        </div>
      </div>
    </motion.div>
  )
}

interface FeatureCardProps {
  title: string
  description: string
  imageSrc: string
  onClick?: () => void
}

function FeatureCard({ title, description, imageSrc, onClick }: FeatureCardProps) {
  return (
    <div
      className="border-[1.5px] border-[#e2e8f0] rounded-lg p-3 flex items-center gap-3 
                 hover:shadow-[0_2px_8px_-2px_rgba(0,0,0,0.08)] 
                 hover:border-[#cbd5e1]
                 transition-all duration-300 ease-in-out 
                 cursor-pointer bg-white/50
                 transform hover:scale-[1.01]"
      onClick={onClick}
    >
      <div className="w-10 h-10 rounded-md overflow-hidden flex-shrink-0 border border-[#e2e8f0]">
        <img
          src={imageSrc || "/placeholder.svg"}
          width={40}
          height={40}
          alt=""
          className="w-full h-full object-cover"
        />
      </div>
      <div>
        <h3 className="font-medium text-[#334155] mb-0.5 text-sm">{title}</h3>
        <p className="text-xs text-[#64748b]">{description}</p>
      </div>
    </div>
  )
}

const createFeatures = [
  {
    title: "Start from scratch",
    description: "Build unique designs from a blank canvas",
    imageSrc: images.startFromScratch,
    type: "start-from-scratch" as AppType
  },
  {
    title: "Lifestyle Photography",
    description: "Stage products in real‑world settings",
    imageSrc: images.lifestylePhotographyImage,
    type: "lifestyle-photography" as AppType
  },
  {
    title: "Product Photography",
    description: "Showcase apparel against a clean backdrop",
    imageSrc: images.productPhotographyImage,
    type: "product-photography" as AppType
  },
  {
    title: "Prints and Patterns",
    description: "Create seamless textile designs instantly",
    imageSrc: images.printsAndPatternsImage,
    type: "prints-and-patterns" as AppType
  },
  {
    title: "Sketch to Render",
    description: "Transform hand sketches into detailed visuals",
    imageSrc: images.sketchToRenderImage,
    type: "sketch-to-render" as AppType
  },
  {
    title: "Technical Drawings",
    description: "Produce precise flats and construction specs",
    imageSrc: images.technicalDrawingsImage,
    type: "technical-drawings" as AppType
  },
  {
    title: "Background Generator",
    description: "Generate custom scenes in one click",
    imageSrc: images.backgroundGeneratorImage,
    type: "background-generator" as AppType
  },
  {
    title: "3D Avatar to Photorealism",
    description: "Turn 3D models into lifelike renderings",
    imageSrc: images.AvatarToPhotorealismImage,
    type: "avatar-to-photorealism" as AppType
  },
  {
    title: "Graphics and Placement Prints",
    description: "Position logos and art on your designs",
    imageSrc: images.graphicsAndPlacement,
    type: "graphics-and-placement" as AppType
  },
  // {
  //   title: "Make PDP assets",
  //   description: "Get creative with clothing, models, background",
  //   imageSrc: images.pdpAssetsImage,
  //   type: "pdp-assets" as AppType
  // },
  // {
  //   title: "Image to Video",
  //   description: "Get creative with clothing, models, background",
  //   imageSrc: images.imageToVideo,
  //   type: "image-to-video" as AppType
  // },
  // {
  //   title: "Create TechPack",
  //   description: "Get creative with clothing, models, background",
  //   imageSrc: images.createTechPackImage,
  //   type: "tech-pack" as AppType
  // }
];

const editFeatures = [
  {
    title: "Start from scratch",
    description: "Get creative with clothing, models, background",
    imageSrc: images.startFromScratch,
    type: "edit" as AppType
  },
  // {
  //   title: "Make garment alterations",
  //   description: "Get creative with clothing, models, background",
  //   imageSrc: images.lifestylePhotographyImage,
  //   type: "garment-alterations" as AppType
  // },
  // {
  //   title: "Change garment color",
  //   description: "Retail or runway fashion on a neutral background",
  //   imageSrc: images.productPhotographyImage,
  //   type: "garment-color" as AppType
  // },
  // {
  //   title: "Trims and embellishments",
  //   description: "Retail or runway fashion on a neutral background",
  //   imageSrc: images.pdpAssetsImage,
  //   type: "trims-embellishments" as AppType
  // },
  // {
  //   title: "Add prints to garments",
  //   description: "Get creative with clothing, models, background",
  //   imageSrc: images.sketchToRenderImage,
  //   type: "add-prints" as AppType
  // }
];

const mixFeatures = [
  {
    title: "Apply Print or Fabric",
    description: "Overlay realistic materials on designs",
    imageSrc: images.patternImage,
    type: "apply-print-or-fabric" as AppType
  },
  // {
  //   title: "Apply Aesthetic",
  //   description: "Retail or runway fashion on a neutral background",
  //   imageSrc: images.aestheticImage,
  //   type: "apply-aesthetic" as AppType
  // },
  // {
  //   title: "Apply Unconventional Material",
  //   description: "Retail or runway fashion on a neutral background",
  //   imageSrc: images.unconventionalImage,
  //   type: "apply-unconventional" as AppType
  // },
  // {
  //   title: "Inspiration Mix",
  //   description: "Retail or runway fashion on a neutral background",
  //   imageSrc: images.inspirationImage,
  //   type: "inspiration-mix" as AppType
  // }
];


