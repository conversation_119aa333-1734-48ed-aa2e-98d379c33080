import type React from "react";
import type { AppType } from "@/types/app.types";

export interface AppConfig {
  id: AppType
  title: string
  showTabs: boolean
  showCanvas: boolean
  component: React.ComponentType
  icon: string
}

export interface Section {
  title?: string
  collapsible?: boolean
  options: ToolOption[]
}

export interface ToolOption {
  type: 'textarea' | 'upload' | 'preset' | 'mode' | 'slider' | 'input' | 'slider-group'
  magicWand?: {
    show: boolean;
    onClick: (currentValue: string) => Promise<void>;
  };
  label?: string
  key?: string
  defaultValue?: number | string
  value?: number | string 
  placeholder?: string
  error?: string
  required?: boolean
  disabled?: boolean
  options?: ToolOption[]
  className?: string
  sliderClassNames?: {
    container?: string
    track?: string
    range?: string
    thumb?: string
    label?: string
    value?: string
  }
  props?: {
    min?: number
    max?: number
    step?: number
    presets?: Array<{
      icon?: React.ReactNode
      label: string
      description?: string
      value?: string
    }>
    modes?: Array<{
      icon?: React.ReactNode
      label: string
      description?: string
      value?: string
    }>
  }
}




