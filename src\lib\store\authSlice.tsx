import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '@/lib/api/api';

const initialState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null
};

// Async thunks for authentication
export const login = createAsyncThunk(
  'login',
  async (credentials: { email: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await api.post('auth/login', credentials);
      console.log('Login response:', response.data);
      return {
        user: {
          email: response.data.user_details?.email || response.data.email,
          full_name: response.data.user_details?.full_name || response.data.full_name
        },
        accessToken: response.data.access_token || response.data.accessToken || response.data.token,
        // No need to store refresh token as it's in HTTP-only cookie
        refreshToken: null
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Lo<PERSON> failed');
    }
  }
);
export const registerUser = createAsyncThunk(
  'registerUser',
  async (credentials: { email: string; full_name?: string; password: string }, { rejectWithValue }) => {
    try {
      const response = await api.post('auth/signup', credentials);
      
      return {
        user: {
          email: response.data.user_details?.email || credentials.email,
          full_name: response.data.user_details?.full_name || credentials.full_name
        },
        accessToken: response.data.access_token || response.data.accessToken || response.data.token,
        refreshToken: response.data.refresh_token || response.data.refreshToken
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Registration failed');
    }
  }
);    
export const refreshToken = createAsyncThunk(
  'refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      // No need to send the refresh token as it's in the cookie - the cookie will be sent automatically
      const response = await api.post('auth/refresh');
      return {
        accessToken: response.data.access_token || response.data.accessToken
      };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Token refresh failed');
    }
  }
);

export const logout = createAsyncThunk(
  'logout',
  async () => {
    try {
      // The backend will clear the cookie
      await api.post('auth/logout');
      return null;
    } catch (error) {
      console.error('Logout error:', error);
      // Still proceed with local logout even if server call fails
      return null;
    }
  }
);


const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setCredentials: (state, action) => {
      const { user, accessToken, refreshToken } = action.payload;
      state.user = user;
      state.accessToken = accessToken;
      state.refreshToken = refreshToken;
      state.isAuthenticated = true;
      
    },
    clearCredentials: (state) => {
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
    }
  },
  extraReducers: (builder) => {
    // Login
    builder.addCase(login.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(login.fulfilled, (state, action) => {
      state.isLoading = false;
      state.user = action.payload.user;
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
      state.isAuthenticated = true;
    });
    builder.addCase(login.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    });
    
    // Refresh token
    builder.addCase(refreshToken.fulfilled, (state, action) => {
      state.accessToken = action.payload.accessToken;
      // Some APIs might also refresh the refresh token
      if ('refreshToken' in action.payload) {
        state.refreshToken = action.payload.refreshToken;
      } 
    });
    builder.addCase(refreshToken.rejected, (state) => {
      // If refresh fails, user needs to log in again
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
    });
    
    // Logout
    builder.addCase(logout.fulfilled, (state) => {
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.isAuthenticated = false;
    });

    // Register
    builder.addCase(registerUser.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(registerUser.fulfilled, (state, action) => {
      state.isLoading = false;
      state.user = action.payload.user;
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
      state.isAuthenticated = true;
    });
    builder.addCase(registerUser.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    });
  }
});

export const { setCredentials, clearCredentials } = authSlice.actions;
export default authSlice.reducer;
