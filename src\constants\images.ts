//Main Page Image

import mainModelImage from "../assets/images/20250408-2146-purple-fashion-interface-remix-01jrb30fjde39t0xyj3.png";

// Fashion Designer Journey Images
import fashionDesignerImage1 from "../assets/images/carousel1.png";
import fashionDesignerImage2 from "../assets/images/carousel2.png";
import fashionDesignerImage3 from "../assets/images/carousel3.png";


// Category Images
import clothingImage from "../assets/images/clothing.png";
import runwayImage from "../assets/images/runway.png";
import jewelryImage from "../assets/images/jewelry.png";
import  footwearImage from "../assets/images/footwear.png";
import sneakersImage from "../assets/images/sneakers.png";
import sketchImage from "../assets/images/sketch.png";
import bagsImage from "../assets/images/bags.png";
import watchesImage from "../assets/images/watches.png";


// Icons and UI Elements
import packageIcon from "../assets/images/package.svg";
import frame9 from "../assets/images/frame-9.svg";
import frame11 from "../assets/images/frame-11.svg";
import frame3 from "../assets/images/frame-3.svg";
import frame12 from "../assets/images/frame-12.svg";
import frame4 from "../assets/images/frame-4.svg";
import frame10 from "../assets/images/frame-10.svg";
import frame8 from "../assets/images/frame-8.svg";
import frame1 from "../assets/images/frame-1.svg";
import frame15 from "../assets/images/frame-15.svg";
import frame from "../assets/images/frame.svg";
import nounArrow from "../assets/images/noun-arrow-992134-1-1.svg";
import nounArrowLarge from "../assets/images/noun-arrow-992134-1-2.svg";
import avatarComponent from "../assets/images/components---avatar.svg";
import bookmark from "../assets/images/bookmark.svg";
import instagram from "../assets/images/instagram.svg";
import vector from "../assets/images/vector.svg";
import twitter from "../assets/images/twitter.svg";
import linkedin from "../assets/images/linkedin.png";
import facebook from "../assets/images/facebook.svg";

// Product Images
import image1 from "../assets/images/image.png";
import image2 from "../assets/images/image-1.png";
import image3 from "../assets/images/image-2.png";
import image4 from "../assets/images/image-3.png";
import image5 from "../assets/images/image-4.png";
import image6 from "../assets/images/image-5.png";
import image7 from "../assets/images/image-6.png";
import image8 from "../assets/images/image-7.png";
import fashionShowcase from "../assets/images/image-5-1.png";
import brandIdentity from "../assets/images/BrandIdentity.png";

import logo from "../assets/images/noun-arrow-992134-1-1.svg"
import logo1 from "../assets/images/logo1.svg"
import individual from "../assets/images/individual.png";
import organisation from "../assets/images/organization.png";
import enterprise from "../assets/images/enterprise.png";

import contentOwnership from "../assets/images/frame-7.svg";
import dataPrivacy from "@/assets/images/frame-14.svg";
import peaceOfMind from "@/assets/images/frame-5.svg";

import dashboard from "../assets/images/dashboard.png";
import loginImage from "../assets/images/loginImage.png" 
import trendGptImage from "../assets/images/TrendGPTImage.png"
import lifestylePhotographyImage from "../assets/images/lifestyleImage.png"
import productPhotographyImage from "../assets/images/productPhotography.png"
import sketchToRenderImage from "../assets/images/SketchToRender.png"
import technicalDrawingsImage from "../assets/images/TechnicalDrawings.png"
import backgroundGeneratorImage from "../assets/images/backgroundGenerator.png"
import AvatarToPhotorealismImage from "../assets/images/AvatarToPhoto.png"
import pdpAssetsImage from "../assets/images/PDPassets.png"
import imageToVideo from "../assets/images/ImageToVideo.png"
import createTechPackImage from "../assets/images/TechPack.png"
import startFromScratch from "../assets/images/startFromScratch.png"
import graphicsAndPlacement from "../assets/images/graphicsAndPlacement.png"


import createIcon from "../assets/images/createIcon.png"
import editIcon from "../assets/images/editIcon.png"
import mixIcon from "../assets/images/mixIcon.png"

import patternImage from "../assets/images/patternImage.png"
import aestheticImage from "../assets/images/aestheticImage.png"
import unconventionalImage from "../assets/images/unconventionalImage.png"
import inspirationImage from "../assets/images/inspirationImage.png"


// landing page game changing AI tools images 

import productDesignLandingPageImage from "../assets/images/LandingPageImages/ProductImage.png"
import trendAnalysisLandingPageImage from "../assets/images/LandingPageImages/TrendAnalysis.png"
import avatarToPhotorealismLandingPageImage from "../assets/images/LandingPageImages/AvatarToPhoto.png"
import backgroundGeneratorLandingPageImage from "../assets/images/LandingPageImages/BackgroundGenerator.png"
import lifestylePhotographyLandingPageImage from "../assets/images/LandingPageImages/LifestylePhotography.png"
import imageMixer from "../assets/images/LandingPageImages/ImageMixer.png"
import infiniteColorways from "../assets/images/LandingPageImages/InfiniteColorways.png"
import technicalDrawingsLandingPageImage from "../assets/images/LandingPageImages/TechnicalDrawings.png"
import seamlessPrints from "../assets/images/LandingPageImages/SeamlessPrints.png"
import preciseEdit from "../assets/images/LandingPageImages/PreciseEdit.png"
import sketchToRenderLandingPageImage from "../assets/images/LandingPageImages/SketchToRender.png"

import printsAndPatternsImage from "../assets/images/PrintsAndPatterns.png"
import abstractPrintImage from "../assets/images/abstractPrints.webp"
import beachNeutralsImage from "../assets/images/beachNeutrals.webp"
import cleanGirlFits from "../assets/images/cleanGirlFits.webp"
import flirtyBlackDress from "../assets/images/flirtyBlackDress.webp"
import kaftans from "../assets/images/kaftans.webp"
import tankTops from "../assets/images/tankTops.jpg"

import lensButton from "../assets/images/lensButton.png"


export default {

    // Main Page Image
    mainModelImage,

    // Fashion Designer Journey
    fashionDesignerImage1,
    fashionDesignerImage2,
    fashionDesignerImage3,

    // Category Images
    clothingImage,
    runwayImage,
    jewelryImage,
    bagsImage,
    watchesImage,
    footwearImage,
    sneakersImage,
    sketchImage,

    // Icons and UI Elements
    packageIcon,
    frame1,
    frame3,
    frame4,
    frame8,
    frame9,
    frame10,
    frame11,
    frame12,
    frame15,
    frame,
    nounArrow,
    nounArrowLarge,
    avatarComponent,
    bookmark,
    instagram,
    vector,
    twitter,
    linkedin,
    facebook,

    // Product Images
    image1,
    image2,
    image3,
    image4,
    image5,
    image6,
    image7,
    image8,
    fashionShowcase,
    brandIdentity,

    // Logo
    logo,

    //individual, org, enterprise
    individual,
    organisation,
    enterprise,

    patternImage,
    aestheticImage,
    unconventionalImage,
    inspirationImage,


    contentOwnership,
    dataPrivacy,
    peaceOfMind,
    dashboard,
    loginImage,
    logo1,
    trendGptImage,
    lifestylePhotographyImage,
    productPhotographyImage,
    sketchToRenderImage,
    technicalDrawingsImage,
    backgroundGeneratorImage,
    AvatarToPhotorealismImage,
    pdpAssetsImage,
    imageToVideo,
    createTechPackImage,
    startFromScratch,
    graphicsAndPlacement,
    createIcon,
    editIcon,
    mixIcon,

    // landing page game changing AI tools images 

    productDesignLandingPageImage,
    trendAnalysisLandingPageImage,
    avatarToPhotorealismLandingPageImage,
    backgroundGeneratorLandingPageImage,
    lifestylePhotographyLandingPageImage,
    imageMixer,
    infiniteColorways,
    technicalDrawingsLandingPageImage,
    seamlessPrints,
    preciseEdit,
    sketchToRenderLandingPageImage,
    printsAndPatternsImage,
    abstractPrintImage,
    beachNeutralsImage,
    cleanGirlFits,
    flirtyBlackDress,
    kaftans,
    tankTops,
    lensButton

}
