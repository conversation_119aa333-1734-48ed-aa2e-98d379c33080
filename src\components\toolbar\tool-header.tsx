interface ToolHeaderProps {
  title: string
  icon?: string
  type?: 'edit' | 'create' | 'mix'
}

export function ToolHeader({ title, icon, type = 'create' }: ToolHeaderProps) {
  return (
    <div className="mb-3 bg-white rounded-lg border border-[#e2e8f0] p-3.5">
      <div className="flex items-start gap-3">
        <div className="w-11 h-11 overflow-hidden flex-shrink-0">
          <img src={icon} alt="" className="w-full h-full object-cover border rounded"/>
        </div>
        <div className="flex flex-col">
          <div className="text-[#6938ef] text-[14px] font-bold">
            {type.toUpperCase()}
          </div>
          <h2 className="font-medium text-base">{title}</h2>
        </div>
      </div>
    </div>
  )
}
