import { useSelector } from "react-redux";
import { createSelector } from "@reduxjs/toolkit";
import { useState } from "react";
import type { RootState } from "@/lib/store/store";
import { ImageGrid } from "./shared/image-grid";
import { images } from "@/constants";
import { cn } from "@/lib/utils";

const selectGeneratedImages = (state: RootState) => state.app.generatedImages;
const selectActiveTabId = (state: RootState) => state.app.activeTabId;
const selectIsLoading = (state: RootState) => state.app.isLoading.images;

const selectCurrentTabImages = createSelector(
    [selectActiveTabId, selectGeneratedImages],
    (activeTabId, generatedImages) => 
        activeTabId ? generatedImages[activeTabId] || [] : []
);

const selectCurrentTabLoading = createSelector(
    [selectActiveTabId, selectIsLoading],
    (activeTabId, isLoading) => 
        activeTabId ? isLoading[activeTabId] || false : false
);

export function StartFromScratchCanvas() {
    const generatedImages = useSelector(selectCurrentTabImages);
    const isLoading = useSelector(selectCurrentTabLoading);

    const exampleImages = [
        images.bagsImage,
        images.lifestylePhotographyImage,
        images.image7
    ];

    return (
        <div className="flex-1 flex flex-col h-[calc(100vh-64px)]">
            <div className="flex-1 flex flex-col m-3 rounded-2xl bg-white border border-[#e2e8f0] overflow-hidden">
                <div className="flex-1 overflow-auto">
                    {generatedImages.length > 0 || isLoading ? (
                        <div className="h-full">
                            <ImageGrid 
                                images={generatedImages} 
                                className="min-h-full"
                                isLoading={isLoading}
                                loadingCount={4}
                            />
                        </div>
                    ) : (
                        <div className="h-full flex items-center justify-center text-[#717680]">
                            <p>Generated images will appear here</p>
                        </div>
                    )}
                </div>

                <CanvasTabs exampleImages={exampleImages} />
            </div>
        </div>
    );
}

function CanvasTabs({ exampleImages }: { exampleImages: string[] }) {
    const generatedImages = useSelector(selectCurrentTabImages);
    const [activeTab, setActiveTab] = useState<"examples" | "history">("examples");

    return (
        <div className="border-t border-[#e2e8f0] bg-[#f8fafc] p-4 space-y-4">
            <div className="flex items-center justify-between gap-4">
                <div className="flex bg-[#f4f6f8] rounded-lg p-0.5">
                    {["Examples", "History"].map((tab) => (
                        <button
                            key={tab}
                            className={cn(
                                "px-4 py-1.5 text-xs font-medium rounded-md transition-all",
                                activeTab.toLowerCase() === tab.toLowerCase()
                                    ? "bg-white text-[#6938ef] shadow-sm"
                                    : "text-gray-600 hover:text-[#6938ef]"
                            )}
                            onClick={() => setActiveTab(tab.toLowerCase() as typeof activeTab)}
                        >
                            {tab}
                        </button>
                    ))}
                </div>
            </div>

            <div className="relative">
                <div className="overflow-x-auto pb-2 -mx-4 px-4">
                    <div className="min-h-[160px]"> 
                        {activeTab === "history" && generatedImages.length === 0 ? (
                            <div className="flex items-center justify-center h-[160px] text-[#717680] text-sm">
                                No generation history yet. Generated images will appear here.
                            </div>
                        ) : (
                            <div className="flex gap-3">
                                {(activeTab === "examples" ? exampleImages : generatedImages).map((image, index) => (
                                    <div
                                        key={index}
                                        className="group relative flex-shrink-0 w-[120px] aspect-[3/4] rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-all cursor-pointer"
                                    >
                                        <img
                                            src={image}
                                            alt=""
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
