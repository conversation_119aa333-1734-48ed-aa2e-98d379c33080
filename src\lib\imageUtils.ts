import * as fabric from "fabric";

export async function generateMaskFromCanvas(canvas: fabric.Canvas): Promise<string> {
    if (!canvas) {
        throw new Error("Fabric canvas is not initialized.");
    }

    const maskCanvas = document.createElement("canvas");
    maskCanvas.width = canvas.width!;
    maskCanvas.height = canvas.height!;
    const maskCtx = maskCanvas.getContext("2d");

    if (!maskCtx) {
        throw new Error("Could not get 2D context for mask canvas");
    }

    // Initialize mask canvas with black background
    maskCtx.fillStyle = "#000000";
    maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);

    // Get current canvas state as data URL
    const canvasDataURL = canvas.toDataURL({
        multiplier: 1,
        format: "png",
        quality: 1,
    });

    // Draw the canvas content onto a temporary image to get pixel data
    const tempImg = new Image();
    tempImg.crossOrigin = "anonymous";

    await new Promise<void>((res, rej) => {
        tempImg.onload = () => res();
        tempImg.onerror = () => rej(new Error("Failed to load canvas data URL for mask generation."));
        tempImg.src = canvasDataURL;
    });

    const processCanvas = document.createElement("canvas");
    processCanvas.width = canvas.width!;
    processCanvas.height = canvas.height!;
    const processCtx = processCanvas.getContext("2d")!;
    processCtx.drawImage(tempImg, 0, 0);

    // Get pixel data from the temporary canvas
    const imageData = processCtx.getImageData(
        0,
        0,
        processCanvas.width,
        processCanvas.height
    ).data;

    // Create new ImageData for the black and white mask
    const maskImageData = new ImageData(
        processCanvas.width,
        processCanvas.height
    );
    const maskData = maskImageData.data;

    // Iterate through pixels to create the mask based on the brush color
    for (let i = 0; i < imageData.length; i += 4) {
        const r = imageData[i];
        const g = imageData[i + 1];
        const b = imageData[i + 2];
        const a = imageData[i + 3];

        // This condition assumes the brush color rgba(102, 51, 153, 0.9)
        // Adjust these thresholds if the brush color changes significantly
        const isAnnotation =
            r > 80 && r < 130 && // Check Red channel
            g > 30 && g < 80 &&  // Check Green channel
            b > 130 && b < 180 && // Check Blue channel
            a > 0;              // Check Alpha channel (not fully transparent)

        if (isAnnotation) {
            // White pixel for annotated area
            maskData[i] = 255;
            maskData[i + 1] = 255;
            maskData[i + 2] = 255;
            maskData[i + 3] = 255; // Fully opaque
        } else {
            // Black pixel for non-annotated area
            maskData[i] = 0;
            maskData[i + 1] = 0;
            maskData[i + 2] = 0;
            maskData[i + 3] = 255; // Fully opaque
        }
    }

    // Put the generated mask pixel data onto the mask canvas
    maskCtx.putImageData(maskImageData, 0, 0);

    // Return the mask canvas as a Data URL
    return maskCanvas.toDataURL("image/png");
}

export async function createWhiteAnnotationPreviewFromCanvas(
    canvas: fabric.Canvas,
    originalImage: fabric.Image
): Promise<string> {
    if (!canvas) {
        throw new Error("Fabric canvas is not initialized.");
    }
    if (!originalImage) {
         throw new Error("Original image is not available.");
    }

    const tempCanvas = document.createElement("canvas");
    tempCanvas.width = canvas.width!;
    tempCanvas.height = canvas.height!;
    const tempCtx = tempCanvas.getContext("2d")!;

    // Clear the canvas and draw the original image
    tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);
    const origEl = originalImage.getElement();
    tempCtx.drawImage(
        origEl,
        originalImage.left!,
        originalImage.top!,
        originalImage.width! * originalImage.scaleX!,
        originalImage.height! * originalImage.scaleY!
    );

    // Get the current canvas state (with annotations) as a data URL
    const canvasDataURL = canvas.toDataURL({
        multiplier: 1,
        format: "png",
        quality: 1,
    });

    // Draw the current canvas state onto a temporary image to get pixel data
    const img = new Image();
    img.crossOrigin = "anonymous";
    await new Promise<void>((res, rej) => {
        img.onload = () => res();
        img.onerror = () => rej(new Error("Failed to load canvas data URL for white annotation preview."));
        img.src = canvasDataURL;
    });

    const procCanvas = document.createElement("canvas");
    procCanvas.width = canvas.width!;
    procCanvas.height = canvas.height!;
    const procCtx = procCanvas.getContext("2d")!;
    procCtx.drawImage(img, 0, 0);

    // Get pixel data from the temporary canvas (which includes annotations)
    const data = procCtx.getImageData(
        0,
        0,
        procCanvas.width,
        procCanvas.height
    ).data;

    // Create new ImageData for the white annotation preview
    const whiteDataImg = procCtx.getImageData(
        0,
        0,
        procCanvas.width,
        procCanvas.height
    );
    const whiteData = whiteDataImg.data;

    // Iterate through pixels to make annotations white and keep original image pixels
    for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];
        const a = data[i + 3];

        // This condition assumes the brush color rgba(102, 51, 153, 0.9)
        // Adjust these thresholds if the brush color changes significantly
        const isAnnot =
            r > 80 && r < 130 && // Check Red channel
            g > 30 && g < 80 &&  // Check Green channel
            b > 130 && b < 180 && // Check Blue channel
            a > 0;              // Check Alpha channel (not fully transparent)

        if (isAnnot) {
            // Make annotation pixels white
            whiteData[i] = 255;
            whiteData[i + 1] = 255;
            whiteData[i + 2] = 255;
            whiteData[i + 3] = 255; // Fully opaque
        } else {
            // Keep original image pixels, but make background transparent if it was the default canvas background color
            whiteData[i] = data[i];
            whiteData[i + 1] = data[i + 1];
            whiteData[i + 2] = data[i + 2];
             // Check if the pixel is the default canvas background color (#f0f0f0) and make it transparent
            if (
                data[i] === 240 &&
                data[i + 1] === 240 &&
                data[i + 2] === 240
            ) {
                whiteData[i + 3] = 0; // Make it transparent
            } else {
                whiteData[i + 3] = data[i + 3]; // Keep original alpha
            }
        }
    }

    // Put the generated pixel data onto the processing canvas
    procCtx.putImageData(whiteDataImg, 0, 0);

    // Return the canvas as a Data URL
    return procCanvas.toDataURL("png");
} 