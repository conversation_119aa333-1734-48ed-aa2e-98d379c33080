import { X, Loader2 } from "lucide-react";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setGeneratedImages } from "@/lib/store/appSlice";
import type { RootState } from "@/types/store.types";
import { upscaleImage, removeBackground, vectorizeImage } from "@/lib/api";
import Lottie from "lottie-react";
import lottie from "@/constants/lottie";

interface ImageDetailModalProps {
    isOpen: boolean;
    onClose: () => void;
    imageUrl: string;
    prompt?: string;
    generatedDate?: string;
}

export function ImageDetailModal({
    isOpen,
    onClose,
    imageUrl,
    prompt = "A stylish young woman with long dark hair, glowing skin, and bold gold leaf earrings poses in profile against a warm brown background, wearing a red blazer in an elegant, modern fashion editorial style",
    generatedDate = "2024 Oct 25 2:58 PM",
}: ImageDetailModalProps) {
    const dispatch = useDispatch();
    const activeTabId = useSelector((state: RootState) => state.app.activeTabId);
    const generatedImages = useSelector((state: RootState) => 
        activeTabId ? state.app.generatedImages[activeTabId] || [] : []
    );
    const [isUpscaling, setIsUpscaling] = useState(false);
    const [isRemovingBackground, setIsRemovingBackground] = useState(false);
    const [currentImage, setCurrentImage] = useState(imageUrl);
    const [isDownloadingSVG, setIsDownloadingSVG] = useState(false);

    // Update currentImage when imageUrl prop changes
    useEffect(() => {
        setCurrentImage(imageUrl);
    }, [imageUrl]);

    // Close on escape key
    useEffect(() => {
        const handleEsc = (event: KeyboardEvent) => {
            if (event.key === "Escape") onClose();
        };

        window.addEventListener("keydown", handleEsc);
        return () => window.removeEventListener("keydown", handleEsc);
    }, [onClose]);

    // Close on background click
    const handleBackdropClick = (e: React.MouseEvent) => {
        if (e.target === e.currentTarget) onClose();
    };

    // Handle download image
    const handleDownload = async () => {
        try {
            const response = await fetch(imageUrl);
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `generated-image-${Date.now()}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Download failed:', error);
        }
    };

    // Handle export as svg image
    const handleSVGExport = async () => {
        try {
            setIsDownloadingSVG(true);
            // Convert image URL to base64 if needed
            let base64Image = currentImage;
            if (!currentImage.startsWith('data:')) {
                const response = await fetch(currentImage);
                const blob = await response.blob();
                base64Image = await new Promise(resolve => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result as string);
                    reader.readAsDataURL(blob);
                });
            }
            // Call the API to vectorize the image
            let svgDataBase64 = await vectorizeImage(base64Image);
            if (svgDataBase64.startsWith('data:image/svg+xml;base64,')) {
                svgDataBase64 = svgDataBase64.replace('data:image/svg+xml;base64,', '');
            }
            const svgContent = atob(svgDataBase64);

            // Create a download link for the SVG data
            const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
            const svgUrl = URL.createObjectURL(svgBlob);
            const downloadLink = document.createElement('a');
            downloadLink.href = svgUrl;
            downloadLink.download = `generated-image-${Date.now()}.svg`;
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            URL.revokeObjectURL(svgUrl);
        } catch (error) {
            console.error('SVG Download failed:', error);
        } finally {
            setIsDownloadingSVG(false);
        }
    };

    // Handle upscale image
    const handleUpscale = async () => {
        if (!activeTabId) return;
        
        try {
            setIsUpscaling(true);
            
            // Convert image URL to base64 if needed
            let base64Image = currentImage;
            if (!currentImage.startsWith('data:')) {
                const response = await fetch(currentImage);
                const blob = await response.blob();
                base64Image = await new Promise(resolve => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result as string);
                    reader.readAsDataURL(blob);
                });
            }
            
            // Call the API to upscale the image
            const upscaledImage = await upscaleImage(base64Image);
            
            // Update the current image in the modal
            setCurrentImage(upscaledImage);
            
            // Replace the original image with the upscaled one in the state
            const updatedImages = [...generatedImages];
            const imageIndex = updatedImages.findIndex(img => img === imageUrl);
            
            if (imageIndex !== -1) {
                updatedImages[imageIndex] = upscaledImage;
                
                dispatch(setGeneratedImages({
                    tabId: activeTabId,
                    images: updatedImages
                }));
            }
        } catch (error) {
            console.error('Upscale failed:', error);
        } finally {
            setIsUpscaling(false);
        }
    };

    // Handle remove background
    const handleRemoveBackground = async () => {
        if (!activeTabId) return;
        
        try {
            setIsRemovingBackground(true);
            
            // Convert image URL to base64 if needed
            let base64Image = currentImage;
            if (!currentImage.startsWith('data:')) {
                const response = await fetch(currentImage);
                const blob = await response.blob();
                base64Image = await new Promise(resolve => {
                    const reader = new FileReader();
                    reader.onloadend = () => resolve(reader.result as string);
                    reader.readAsDataURL(blob);
                });
            }
            
            // Call the API to remove background
            const processedImage = await removeBackground(base64Image);
            
            // Update the current image in the modal
            setCurrentImage(processedImage);
            
            // Replace the original image with the processed one in the state
            const updatedImages = [...generatedImages];
            const imageIndex = updatedImages.findIndex(img => img === imageUrl);
            
            if (imageIndex !== -1) {
                updatedImages[imageIndex] = processedImage;
                
                dispatch(setGeneratedImages({
                    tabId: activeTabId,
                    images: updatedImages
                }));
            }
        } catch (error) {
            console.error('Background removal failed:', error);
        } finally {
            setIsRemovingBackground(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4"
            onClick={handleBackdropClick}
        >
            <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="bg-white rounded-lg overflow-hidden max-w-6xl w-full max-h-[100vh] flex"
            >
                {/* Left side - Image */}
                <div className="flex-1 bg-gray-50 relative">
                    {(isUpscaling || isRemovingBackground) ? (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-50/80">
                            <Lottie
                                animationData={lottie.lottieLoader}
                                loop={true}
                                className="w-[150%] h-[150%]"
                            />
                        </div>
                    ) : null}
                    <img
                        src={currentImage}
                        alt="Generated image"
                        className="w-full h-full object-contain"
                    />
                </div>

                {/* Right side - Details & Actions */}
                <div className="w-96 bg-white p-6 flex flex-col h-full">
                    <div className="space-y-6 flex-1 overflow-y-auto">
                        <div>
                            <div className="flex justify-between">
                                <h4 className="text-sm font-medium text-gray-500 mb-1">
                                    Generated
                                </h4>
                                <button
                                    onClick={onClose}
                                    className="text-gray-400  cursor-pointer hover:text-gray-500"
                                >
                                    <X size={20} />
                                </button>
                            </div>
                            <p className="text-sm">{generatedDate}</p>
                        </div>

                        <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">
                                Prompt
                            </h4>
                            <p className="text-sm text-gray-700">{prompt}</p>
                        </div>

                        <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">
                                Created At
                            </h4>
                            <p className="text-sm">{generatedDate}</p>
                        </div>
                    </div>

                    <div className="mt-6 space-y-3 pt-6 border-t">
                        <button className="w-full py-2 px-4 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors cursor-pointer">
                            Open in AI Editor
                        </button>
                        <button 
                            className="w-full py-2 px-4 bg-white text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors cursor-pointer"
                            onClick={handleDownload}
                        >
                            Download Image
                        </button>
                        <button 
                            className="w-full py-2 px-4 bg-white text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                            onClick={handleRemoveBackground}
                            disabled={isRemovingBackground}
                        >
                            {isRemovingBackground ? (
                                <span className="flex items-center justify-center">
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    Removing Background...
                                </span>
                            ) : (
                                "Remove Background"
                            )}
                        </button>
                        <button 
                            className="w-full py-2 px-4 bg-white text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                            onClick={handleSVGExport}
                            disabled={isDownloadingSVG}
                        >
                            {isDownloadingSVG ? (
                                <span className="flex items-center justify-center">
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                    Preparing SVG Export...
                                </span>
                            ) : (
                                "Download as SVG"
                            )}
                        </button>
                        <div className="grid grid-cols-2 gap-3">
                            <button 
                                className="py-2 px-4 bg-white text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                                onClick={handleUpscale}
                                disabled={isUpscaling}
                            >
                                {isUpscaling ? (
                                    <span className="flex items-center justify-center">
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        Upscaling...
                                    </span>
                                ) : (
                                    "Upscale"
                                )}
                            </button>
                            <button className="py-2 px-4 bg-white text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors cursor-pointer">
                                Enhance Image
                            </button>
                        </div>
                        <div className="grid grid-cols-2 gap-3">
                            <button className="py-2 px-4 bg-white text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors cursor-pointer">
                                Variations
                            </button>
                            <button className="py-2 px-4 bg-white text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors cursor-pointer">
                                Regenerate
                            </button>
                        </div>
                        <button className="w-full py-2 px-4 bg-white text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors cursor-pointer">
                            Reverse Image Search
                        </button>
                    </div>
                </div>
            </motion.div>
        </div>
    );
}
