<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="72" height="72" rx="36" fill="url(#paint0_linear_96_962)"/>
<g filter="url(#filter0_dii_96_962)">
<path d="M58.8019 18.0719L23.682 29.1648C22.7134 29.4651 22.3996 30.6928 23.1364 31.4297L29.4809 37.7742L13.9677 51.6095C12.8897 52.5647 13.8312 54.2972 15.2092 53.9562L51.3116 44.9102C52.3348 44.6506 52.7033 43.382 51.9528 42.6179L45.2537 35.9325L60.2075 20.31C61.1761 19.2863 60.1255 17.6355 58.8019 18.0719Z" fill="#FFD900"/>
</g>
<defs>
<filter id="filter0_dii_96_962" x="4.5" y="9" width="65.085" height="54" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_96_962"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_96_962" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_96_962"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_96_962" result="effect3_innerShadow_96_962"/>
</filter>
<linearGradient id="paint0_linear_96_962" x1="0" y1="0" x2="72" y2="72" gradientUnits="userSpaceOnUse">
<stop stop-color="#6C3EDF"/>
<stop offset="1" stop-color="#5C0092"/>
</linearGradient>
</defs>
</svg>
