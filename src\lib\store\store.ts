import { configureStore } from "@reduxjs/toolkit"
import { persistStore, persistReducer } from "redux-persist"
import { combineReducers } from "redux"
import appReducer from "./appSlice"
import sidebarReducer from "./sidebarSlice"
import authReducer from "./authSlice"
import trendGptReducer from "./trendGptSlice"
import localforage from 'localforage';

const authPersistConfig = {
  key: "auth",
  storage: localforage,
  whitelist: ["accessToken", "refreshToken", "user", "isAuthenticated", "isLoading", "error"],
}


// Create a user-specific localforage wrapper
const createUserLocalForage = () => {
  return {
    getItem: async (key) => {
      const authData = JSON.parse(localStorage.getItem('persist:auth') || '{}');
      const userObj = authData.user ? JSON.parse(authData.user) : null;
      const userKey = userObj?.email ? `${key}_${userObj.email}` : key;
      return localforage.getItem(userKey);
    },
    setItem: async (key, value) => {
      const authData = JSON.parse(localStorage.getItem('persist:auth') || '{}');
      const userObj = authData.user ? JSON.parse(authData.user) : null;
      const userKey = userObj?.email ? `${key}_${userObj.email}` : key;
      return localforage.setItem(userKey, value);
    },
    removeItem: async (key) => {
      const authData = JSON.parse(localStorage.getItem('persist:auth') || '{}');
      const userObj = authData.user ? JSON.parse(authData.user) : null;
      const userKey = userObj?.email ? `${key}_${userObj.email}` : key;
      return localforage.removeItem(userKey);
    }
  };
};

const userLocalForage = createUserLocalForage();

const rootPersistConfig = {
  key: "root",
  storage: userLocalForage,
  whitelist: ["app", "sidebar", "auth", "trendGpt"],
}

const rootReducer = combineReducers({
  app: appReducer,
  sidebar: sidebarReducer,
  auth: persistReducer(authPersistConfig, authReducer),
  trendGpt: trendGptReducer,
})

const persistedReducer = persistReducer(rootPersistConfig, rootReducer)

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          "persist/PERSIST", 
          "persist/REHYDRATE", 
          "persist/REGISTER",
          // Add auth async thunk actions to ignored list for serialization checks
          "auth/login/fulfilled",
          "auth/refreshToken/fulfilled"
        ],
      },
    }),
})

export const persistor = persistStore(store)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
