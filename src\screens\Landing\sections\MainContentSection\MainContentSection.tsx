import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { images } from "@/constants"
import WaitlistModal from "../WaitlistModal/WaitlistModal"
import { useNavigate } from "react-router-dom"

export const MainContentSection = () => {
  // const navItems = [
  //   { name: "Solutions", hasDropdown: true },
  //   { name: "About Us", hasDropdown: false },
  //   { name: "Pricing", hasDropdown: false },
  //   { name: "Security", hasDropdown: false },
  // ];
  const navigate = useNavigate()

  return (
    <section className="relative w-full flex flex-col items-center px-6 py-6 md:pl-18 md:pr-18 ">
      {/* Navigation Bar */}
      <div className="flex w-full max-w-[1440px] items-center justify-between mb-4 md:mb-6">
        {/* Logo */}
        <div className="flex items-center gap-2">
          <img className="w-[22px] h-[22px]" alt="Noun arrow" src={images.logo || "/placeholder.svg"} />
          <div className="[font-family:'Plus_Jakarta_Sans',Helvetica] font-bold text-black text-lg tracking-[-0.36px]">
            Rapid Runway
          </div>
        </div>

        {/* Navigation Items - Hidden on mobile, visible on larger screens  */}
        {/* commented out for now */}

        {/* <NavigationMenu className="hidden md:flex">
          <NavigationMenuList className="flex items-center gap-6">
            {navItems.map((item) => (
              <NavigationMenuItem key={item.name}>
                {item.hasDropdown ? (
                  <div className="flex items-end gap-0.5">
                    <NavigationMenuTrigger className="[font-family:'Plus_Jakarta_Sans',Helvetica] font-medium text-black text-xs p-0 bg-transparent h-auto">
                      {item.name}
                    </NavigationMenuTrigger>
                    <NavigationMenuContent>
                    </NavigationMenuContent>
                  </div>
                ) : (
                  <div className="[font-family:'Plus_Jakarta_Sans',Helvetica] font-medium text-black text-xs">
                    {item.name}
                  </div>
                )}
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu> */}

        {/* CTA Button */}
        <div>
          <WaitlistModal />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col md:flex-row w-full max-w-[1440px] mt-4 md:mt-[40px] gap-6 md:gap-[40px]">
        {/* Left Content */}
        <div className="flex flex-col flex-1 items-start justify-between ">
          {/* Hero Text and CTA */}
          <div className="flex flex-col items-start gap-4">
            <div className="flex flex-col items-start gap-3 w-full">
              <h1 className="[font-family:'Plus_Jakarta_Sans',Helvetica] font-bold text-3xl md:text-[48px] tracking-[-0.88px] leading-tight flex flex-col">
                <span className="text-black tracking-[-0.39px]">Design, validate &amp;</span>
                <span className="text-black tracking-[-0.39px]">manufacture trending</span>
                <span>
                  <span className="text-black tracking-[-0.39px]">apparel </span>
                  <span className="text-[#6e56cf] tracking-[-0.39px]">in days</span>
                  <span className="text-black tracking-[-0.39px]">, not months.</span>
                </span>
              </h1>
              <p className="text-stone-500 text-base md:text-lg [font-family:'Inter',Helvetica] font-normal leading-[24px] md:leading-[28px] max-w-[600px]">
                Reimagine your entire fashion workflow, from concept sketch to shelf-ready products, at a pace the
                industry has never seen before.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4 mb-1">
              <WaitlistModal />
              <Button
                variant="outline"
                className="w-full sm:w-auto bg-white rounded-md px-8 py-2 h-auto border-[#d4dae1]"
                onClick={() => navigate("/login")}
              >
                <span className="[font-family:'Plus_Jakarta_Sans',Helvetica] font-medium text-slate-950 text-sm leading-5 cursor-pointer">
                  Login
                </span>
              </Button>
            </div>
          </div>

          {/* Timeline Comparison Card */}
          <Card className="w-full md:w-[80%] mt-8 md:mt-12 border-[#e4e4e4] rounded-[16px] p-0">
            <CardContent className="p-4 md:p-6 flex flex-col gap-[16px]">
              <p className="text-stone-500 text-sm md:text-base [font-family:'Inter',Helvetica] font-normal leading-[22px]">
                Analyze trends, iterate designs, validate concepts, manufacture within 45 days
              </p>
              <div className="flex flex-col gap-3 w-full">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-[40px] w-full">
                  <div className="[font-family:'Plus_Jakarta_Sans',Helvetica] font-bold text-[#7e7e7e] text-sm md:text-base tracking-[-0.32px] min-w-[150px]">
                    Current Timelines
                  </div>
                  <div className="relative w-full h-[30px] bg-[#eaeaea] rounded overflow-hidden">
                    <div className="absolute top-1.5 left-4 sm:left-auto sm:right-4 [font-family:'Plus_Jakarta_Sans',Helvetica] font-medium text-[#02061799] text-xs md:text-sm leading-5 whitespace-nowrap">
                      6 - 8 months
                    </div>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-[40px] w-full">
                  <div className="[font-family:'Plus_Jakarta_Sans',Helvetica] font-bold text-brand text-sm md:text-base tracking-[-0.32px] min-w-[150px]">
                    With Rapid Runway
                  </div>
                  <div className="relative w-1/2 sm:w-[150px] h-[30px] bg-[#6c3edf3d] rounded overflow-hidden">
                    <div className="absolute top-1.5 left-4 sm:left-[20px] [font-family:'Plus_Jakarta_Sans',Helvetica] font-medium text-[#000000cc] text-xs md:text-sm leading-5 whitespace-nowrap">
                      45 days
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Image */}
        <div className="w-full md:w-auto flex justify-center md:justify-start mt-6 md:mt-0 md:-ml-6">
          <div className="w-full max-w-[320px] h-[420px] md:max-w-[380px] md:h-[520px] rounded-[16px] overflow-hidden bg-[#f3f3f3]">
            <img
              className="w-full h-full object-cover"
              alt="Fashion interface mockup"
              src={images.mainModelImage || "/placeholder.svg"}
            />
          </div>
        </div>
      </div>
    </section>
  )
}
