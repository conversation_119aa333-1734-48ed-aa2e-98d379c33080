import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useDispatch, useSelector } from "react-redux";
import { 
  startGeneratingImages, 
  setGeneratedImages, 
  updateToolConfiguration,
  setImagesError
} from "@/lib/store/appSlice";
import type { RootState } from "@/types/store.types";
import { ToolConfiguration } from "@/components/toolbar/tool-configuration";
import { ToolLayout } from "@/components/toolbar/tool-layout";
import type { Section } from "@/types/config.types";
import { motion } from "framer-motion";
import { generateApplyPrintOrFabric } from "@/lib/api";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import api from '@/lib/api/api';

// Define the form schema 
const formSchema = z.object({
  productImage: z.string().optional(),
  fabricImage: z.string().optional(),
  mixPercentage: z.number().min(0).max(100).default(50),
  description: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export function ApplyPrintOrFabricTool() {
  const dispatch = useDispatch();
  const activeTabId = useSelector((state: RootState) => state.app.activeTabId);
  const toolConfiguration = useSelector((state: RootState) => 
    activeTabId ? state.app.toolConfigurations[activeTabId] : null
  );
  
  // Track if we have a product description
  const [productDescriptionLoading, setProductDescriptionLoading] = useState(false);
  // Track if we should fetch product description
  const [shouldFetchDescription, setShouldFetchDescription] = useState(false);

  const {
    setValue,
    formState: { errors },
    trigger,
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      mixPercentage: toolConfiguration?.mixPercentage || 50,
      description: toolConfiguration?.description || "",
    },
  });

  // Determine if we should enable fabric upload
  const isFabricUploadEnabled = Boolean(toolConfiguration?.productImage);

  // Get product description when a new product image is uploaded
  useEffect(() => {
    const fetchProductDescription = async () => {
      // Only call describe if we have a product image and either:
      // 1. We don't have a description yet, or
      // 2. We explicitly set shouldFetchDescription to true (for image replacement)
      if (
        activeTabId && 
        toolConfiguration?.productImage && 
        (shouldFetchDescription || !toolConfiguration?.productDescription) &&
        !productDescriptionLoading
      ) {
        try {
          // Mark that we're loading the description
          setProductDescriptionLoading(true);
          // Reset the shouldFetchDescription flag
          setShouldFetchDescription(false);
          
          // Make sure the image is in the correct format
          let imageData = toolConfiguration?.productImage;
          if (!imageData.startsWith('data:')) {
            imageData = `data:image/png;base64,${imageData}`;
          }
          
          const describeResponse = await api.post('/describe', {
            base64EncodedImage: imageData
          });
          
          if (describeResponse.data.status === 'success') {
            // Store the description in the toolConfiguration as productDescription
            dispatch(updateToolConfiguration({
              tabId: activeTabId,
              values: {
                productDescription: describeResponse.data.data
              }
            }));
            

            toast.success('Product image analyzed successfully');
          } else {
            toast.error('Failed to analyze product image');
          }
        } catch (error) {
          console.error(error);
          toast.error('Failed to analyze product image');
        } finally {
          setProductDescriptionLoading(false);
        }
      }
    };

    fetchProductDescription();
  }, [toolConfiguration?.productImage, shouldFetchDescription, activeTabId, dispatch, productDescriptionLoading, toolConfiguration?.productDescription]);

  const sections: Section[] = [
    {
      title: "Mode",
      options: [
        {
          type: 'mode',
          key: 'mode',
          props: {
            modes: [
              { 
                label: 'Seamless Print',
                description: 'Apply a print seamlessly to the product',
                value: 'seamlessPrint'
              },
              { 
                label: 'Graphic Print',
                description: 'Apply a graphic print to the product',
                value: 'graphicPrint'
              },
              {
                label: 'Fabric',
                description: 'Apply a fabric texture to the product',
                value: 'fabric'
              }
            ],
          },
          defaultValue: toolConfiguration?.mode || 'seamlessPrint',
        },
      ]
    },
    {
      title: "Product",
      options: [
        {
          type: 'upload',
          key: 'productImage',
          required: true,
          error: errors.productImage?.message,
        },
      ]
    },
    {
      title: "Fabric or Print",
      options: [
        {
          type: 'upload',
          key: 'fabricImage',
          required: true,
          error: errors.fabricImage?.message,
          disabled: !isFabricUploadEnabled,
        },
      ]
    },
    {
      options: [
        {
          type: 'slider',
          label: 'Mix',
          key: 'mixPercentage',
          defaultValue: toolConfiguration?.mixPercentage || 50,
          props: { min: 0, max: 100, step: 1 },
          sliderClassNames: {
            track: "bg-[#e2e8f0]",
            range: "bg-[#6938ef]",
            thumb: "border-[#6938ef] bg-white hover:bg-[#6938ef]/10"
          }
        },
      ]
    },
    {
      options: [
        {
          type: 'textarea',
          label: 'Describe your desired output',
          key: 'description',
          placeholder: 'Add prompt',
          error: errors.description?.message,
          required: false,
        },
      ]
    }
  ];

  const handleGenerate = async () => {
    if (!activeTabId) {
      return;
    }

    // Check if product image exists first
    if (!toolConfiguration?.productImage) {
      toast.error("Please upload a product image first");
      return;
    }
    
    // Check if fabric image exists
    if (!toolConfiguration?.fabricImage) {
      toast.error("Please upload a fabric or print image");
      return;
    }
    
    // Check if product description has been loaded
    if (productDescriptionLoading) {
      toast.warning("Product image is still being analyzed. Please wait a moment.");
      return;
    }
    
    if (!toolConfiguration?.productDescription) {
      toast.warning("Product analysis failed. Please try uploading the image again.");
      return;
    }

    // Validate other required fields before generating
    const isValid = await trigger();
    if (!isValid) {
      toast.error("Please select all required options");
      return;
    }
    
    dispatch(startGeneratingImages(activeTabId));
    
    try {
      const userPrompt = toolConfiguration?.description || '';
      
      // Use the product description that was fetched when the image was uploaded
      const productDescription = toolConfiguration.productDescription || '';

      const mode = toolConfiguration?.mode || 'seamlessPrint';
  
      // Generate 4 images concurrently with different temperature values
      const temperatures = [0.25, 0.5, 0.75, 1];
      const imagePromises = temperatures.map(temp => 
        generateApplyPrintOrFabric(
          mode,
          userPrompt, 
          toolConfiguration?.productImage,
          toolConfiguration?.fabricImage,
          temp,
          productDescription 
        ) 
      );
      
      const generatedUrls = await Promise.all(imagePromises);
      
      dispatch(
        setGeneratedImages({
          tabId: activeTabId,
          images: generatedUrls
        })
      );
      
    } catch (error) {
      console.error('Image generation failed:', {
        error,
        config: toolConfiguration,
        activeTabId
      });
      
      dispatch(setImagesError('Failed to generate images. Please try again.'));
      toast.error('Failed to generate images. Please try again.');
    }
  };

  const handleValueChange = (values: Record<string, unknown>) => {
    if (!activeTabId) return;
    
    // If user is trying to upload fabric before product image, show error
    if (values.fabricImage && !toolConfiguration?.productImage && !values.productImage) {
      toast.error("Please upload a product image first");
      return;
    }
    
    // If we're changing the product image
    if (values.productImage && values.productImage !== toolConfiguration?.productImage) {
      // Clear any existing description when changing the product image
      values.description = ""; // Changed from null to empty string
      
      // Flag that we need to fetch description for the new product image
      setShouldFetchDescription(true);
    }
    
    // Update form values
    Object.entries(values).forEach(([key, value]) => {
      setValue(key as keyof FormValues, value as undefined, {
        shouldValidate: true,
      });
    });
    
    // Update the tool configuration in the store
    dispatch(updateToolConfiguration({
      tabId: activeTabId,
      values
    }));
  };

  const renderCustomField = (option) => {
    if (option.key === "productImage" || option.key === "fabricImage") {
      // Check if props and options exist before accessing them
      const options = option.props?.options || [];
      if (!options.length) {
        return null; // Return null if no options are available
      }
      
      const selectedId = toolConfiguration?.[option.key] || option.props?.selectedOption;
      const selectedOption = options.find((opt) => opt.id === selectedId) || options[0];
      
      // Check if this is the fabric upload and it should be disabled
      const isDisabled = option.key === "fabricImage" && !isFabricUploadEnabled;

      return (
        <div className="space-y-2">
          {/* Selected Display */}
          {option.props?.renderSelected && selectedOption && (
            <div className="mb-3">{option.props.renderSelected(selectedOption)}</div>
          )}
          
          {/* Options Grid */}
          <div className={`flex gap-2 overflow-x-auto pb-1 ${isDisabled ? 'opacity-50' : ''}`}>
            {options.map((opt) => (
              <button
                key={opt.id}
                onClick={() => !isDisabled && handleValueChange({ [option.key]: opt.id })}
                disabled={isDisabled}
                className={`flex-shrink-0 p-0.5 rounded ${
                  selectedId === opt.id ? 'ring-2 ring-[#6938ef]' : 'ring-1 ring-[#e2e8f0]'
                } ${isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
              >
                {option.props?.renderItem ? option.props.renderItem(opt) : opt.id}
              </button>
            ))}
          </div>
          
          {option.error && (
            <p className="text-red-500 text-xs mt-1">{option.error}</p>
          )}
          
          {/* Helper text for disabled fabric upload */}
          {isDisabled && (
            <p className="text-amber-600 text-xs mt-1">Upload a product image first</p>
          )}
          
          {/* Loading indicator when fetching product description */}
          {option.key === "productImage" && 
           toolConfiguration?.productImage && 
           productDescriptionLoading && (
            <p className="text-blue-600 text-xs mt-1">Analyzing product image...</p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full p-4"
    >
      <ToolLayout>
        <div className="animate-slideIn">
          <ToolConfiguration
            title="Apply Print or Fabric"
            type="mix"
            sections={sections}
            onGenerate={handleGenerate}
            onChange={handleValueChange}
            initialValues={toolConfiguration as unknown as Record<string, unknown> || {}}
            renderCustomField={renderCustomField}
          />
        </div>
      </ToolLayout>
    </motion.div>
  );
}
