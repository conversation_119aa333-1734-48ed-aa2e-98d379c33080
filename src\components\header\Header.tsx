import type React from "react"
import { <PERSON>, Plus, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { useDispatch, useSelector } from "react-redux"
import { motion, AnimatePresence } from "framer-motion"
import type { RootState } from "@/types/store.types"
import { addTab, setActiveTab, closeTab } from "@/lib/store/appSlice"
import { v4 as uuidv4 } from "uuid"
import { UserProfileDropdown } from "./UserProfileDropdown"

interface HeaderProps {
  showTabs?: boolean
}

export function Header({ showTabs = false }: HeaderProps) {
  const dispatch = useDispatch()
  const { tabs, activeTabId } = useSelector((state: RootState) => state.app)

  const handleNewTab = () => {
    if (!tabs.some(tab => tab.type === "dashboard")) {
      dispatch(
        addTab({
          id: uuidv4(),
          type: "dashboard",
          title: "AI Tools",
        })
      )
    }
  }

  const handleTabClick = (id: string) => {
    dispatch(setActiveTab(id))
  }

  const handleCloseTab = (e: React.MouseEvent, id: string) => {
    e.stopPropagation()
    dispatch(closeTab(id))
  }

  return (  
    <div className="h-12 flex items-center min-w-0 bg-[#f8fafc] border-b border-[#e2e8f0] ">
      {/* Left side - Tabs section */}
      {showTabs ? (
        <div className="flex-1 min-w-0 pl-4 pt-1 flex items-center relative scrollbar-hide overflow-auto">
            <div className="flex items-center">
              <AnimatePresence initial={false}>
                {tabs.map((tab) => (
                  <motion.div
                    key={tab.id}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, transition: { duration: 0.15 } }}
                    transition={{ duration: 0.15 }}
                    className={cn(
                      "h-9 px-4 flex items-center gap-2 cursor-pointer whitespace-nowrap rounded-t-lg",
                      "border-r border-t border-l border-[#e2e8f0]",
                      "group shrink-0",
                      activeTabId === tab.id
                        ? "bg-white border-b-0"
                        : "bg-[#f8fafc] hover:bg-[#f4f6f8] border-b border-[#e2e8f0]",
                      "mt-2"
                    )}
                    onClick={() => handleTabClick(tab.id)}
                  >
                    <span className="text-sm font-medium">{tab.title}</span>
                    <button
                      className={cn(
                        "w-5 h-5 rounded-full flex items-center justify-center hover:bg-[#e2e8f0]",
                        activeTabId === tab.id
                          ? "opacity-100"
                          : "opacity-0 group-hover:opacity-100"
                      )}
                      onClick={(e) => handleCloseTab(e, tab.id)}
                    >
                      <X size={14} />
                    </button>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              <motion.button
                layout="position"
                className={cn(
                  "h-8 px-3 flex items-center gap-1 text-[#717680] hover:bg-[#f4f6f8] shrink-0",
                  "border-b border-[#e2e8f0]",
                  "mt-3 bg-[#f8fafc]"
                )}
                onClick={handleNewTab}
              >
                <Plus size={16} />
                <span>New task</span>
              </motion.button>
            </div>
        </div>
      ) : (
        <div className="flex-1">
          {/* Left section - can be used for breadcrumbs or other content when tabs are not shown */}
        </div>
      )}

      {/* Right section - notifications and account */}
      <div className="flex items-center gap-2 px-4">
        <button className="w-8 h-8 rounded-full flex items-center justify-center hover:bg-[#e2e8f0]">
          <Bell size={18} />
        </button>
        <UserProfileDropdown />
      </div>
    </div>
  )
}







