<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="22" height="22" rx="4.125" fill="url(#paint0_linear_80_253)"/>
<g filter="url(#filter0_dii_80_253)">
<path d="M17.9672 5.52197L7.23618 8.91146C6.94022 9.00322 6.84434 9.37835 7.06946 9.60352L9.00804 11.5421L4.26792 15.7696C3.93852 16.0614 4.2262 16.5908 4.64726 16.4866L15.6786 13.7226C15.9912 13.6432 16.1038 13.2556 15.8745 13.0221L13.8275 10.9794L18.3967 6.20582C18.6927 5.89303 18.3717 5.38864 17.9672 5.52197Z" fill="#FFD900"/>
</g>
<defs>
<filter id="filter0_dii_80_253" x="1.375" y="2.75" width="19.8872" height="16.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.375"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_80_253"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_80_253" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.34375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_80_253"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.34375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_80_253" result="effect3_innerShadow_80_253"/>
</filter>
<linearGradient id="paint0_linear_80_253" x1="0" y1="0" x2="22" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#6C3EDF"/>
<stop offset="1" stop-color="#5C0092"/>
</linearGradient>
</defs>
</svg>
