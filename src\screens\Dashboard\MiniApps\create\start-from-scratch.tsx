import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useDispatch, useSelector } from "react-redux";
import { 
  startGeneratingImages, 
  setGeneratedImages, 
  updateToolConfiguration,
  setImagesError
} from "@/lib/store/appSlice";
import type { RootState } from "@/types/store.types";
import { CuboidIcon as Cube, Sun } from "lucide-react";
import { ToolConfiguration } from "@/components/toolbar/tool-configuration";
import { ToolLayout } from "@/components/toolbar/tool-layout";
import type { Section } from "@/types/config.types";
import { motion } from "framer-motion";
import { generateStartFromScratch, improvePrompt } from "@/lib/api";
import { toast } from "sonner";

// Define the form schema
const formSchema = z.object({
  description: z.string().min(1, "Please describe your design"),
  referenceImage: z.string().optional(),
  guidanceScale: z.number().optional(),
  steps: z.number().optional(),
  seed: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

export function StartFromScratchTool() {
  const dispatch = useDispatch();
  const activeTabId = useSelector((state: RootState) => state.app.activeTabId);
  const toolConfiguration = useSelector((state: RootState) => 
    activeTabId ? state.app.toolConfigurations[activeTabId] : null
  );

  const {
    setValue,
    formState: { errors },
    trigger,
    watch,
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: toolConfiguration?.description || "",
      referenceImage: toolConfiguration?.referenceImage || "",
      guidanceScale: toolConfiguration?.guidanceScale || 3.0,
      steps: toolConfiguration?.steps || 30,
      seed: toolConfiguration?.seed || "0",
    },
  });

  // Watch the description field
  const description = watch("description");

  const sections: Section[] = [
    {
      options: [
        {
          type: 'textarea',
          label: 'Describe your design',
          key: 'description',
          placeholder: 'A creative concept for a modern living room...',
          error: errors.description?.message,
          required: true,
          value: description,
          magicWand: {
            show: true,
            onClick: async (currentValue: string) => {
              try {
                const improvedPrompt = await improvePrompt("Start From Scratch", currentValue);
                
                dispatch(updateToolConfiguration({
                  tabId: activeTabId,
                  values: {
                    description: improvedPrompt
                  }
                }));
                
                setValue("description", improvedPrompt, { shouldValidate: true });
                
                toast.success("Prompt improved!");
                return improvedPrompt;
              } catch (error) {
                toast.error("Failed to improve prompt");
                throw error;
              }
            }
          }
        },
        {
          type: 'upload',
          label: 'Reference Image (optional)',
          key: 'referenceImage',
          required: false,
          error: errors.referenceImage?.message,
        },
      ]
    },
    {
      options: [
        {
          type: 'preset',
          label: 'Presets (optional)',
          key: 'presets',
          props: {
            presets: [
              { icon: <Cube size={16} className="text-[#717680]" />, label: 'Perspective' },
              { icon: <Sun size={16} className="text-[#717680]" />, label: 'Lighting' },
            ],
          },
        },
      ]
    },
    {
      title: "ADVANCED",
      collapsible: true,
      options: [
        {
          type: 'slider-group',
          className: "grid grid-cols-2 gap-4",
          options: [
            {
              type: 'slider',
              label: 'Guidance Scale',
              key: 'guidanceScale',
              defaultValue: toolConfiguration?.guidanceScale || 3.0,
              props: { min: 0, max: 10, step: 0.1 },
              sliderClassNames: {
                track: "bg-[#e2e8f0]",
                range: "bg-[#6938ef]",
                thumb: "border-[#6938ef] bg-white hover:bg-[#6938ef]/10"
              }
            },
            {
              type: 'slider',
              label: 'Steps',
              key: 'steps',
              defaultValue: toolConfiguration?.steps || 30,
              props: { min: 10, max: 50, step: 1 },
              sliderClassNames: {
                track: "bg-[#e2e8f0]",
                range: "bg-[#6938ef]",
                thumb: "border-[#6938ef] bg-white hover:bg-[#6938ef]/10"
              }
            },
          ]
        },
        {
          type: 'input',
          label: 'Seed',
          key: 'seed',
          defaultValue: toolConfiguration?.seed || '0',
        },
      ]
    }
  ];

  const handleGenerate = async () => {
    if (!activeTabId) {
      console.warn('No active tab ID found');
      return;
    }

    // Validate the description field before generating
    const isValid = await trigger("description");
    if (!isValid) {
      toast.error("Please provide a design description");
      return;
    }
    
    console.log('Starting image generation with config:', toolConfiguration);
    dispatch(startGeneratingImages(activeTabId));
    
    try {
      const description = toolConfiguration?.description || '';
      const referenceImage = toolConfiguration?.referenceImage || '';
      
      // Generate 4 images concurrently with different temperature values
      const temperatures = [0.25, 0.5, 0.75, 1];
      const imagePromises = temperatures.map(temp => 
        generateStartFromScratch(description, referenceImage, temp)
      );
      
      const generatedUrls = await Promise.all(imagePromises);
      
      dispatch(
        setGeneratedImages({
          tabId: activeTabId,
          images: generatedUrls
        })
      );
      
      console.log('Successfully generated 4 images');
    } catch (error) {
      console.error('Image generation failed:', {
        error,
        config: toolConfiguration,
        activeTabId
      });
      
      dispatch(setImagesError('Failed to generate images. Please try again.'));
      toast.error('Failed to generate images. Please try again.');
    }
  };

  const handleValueChange = (values: Record<string, unknown>) => {
    if (!activeTabId) return;
    
    // Update form values
    Object.entries(values).forEach(([key, value]) => {
      setValue(key as keyof FormValues, value as undefined, {
        shouldValidate: true,
      });
    });
    
    dispatch(updateToolConfiguration({
      tabId: activeTabId,
      values
    }));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full p-4"
    >
      <ToolLayout>
        <div className="animate-slideIn">
          <ToolConfiguration
            title="Start From Scratch"
            type="create"
            sections={sections}
            onGenerate={handleGenerate}
            onChange={handleValueChange}
            initialValues={{
              description: description,
              referenceImage: watch("referenceImage"),
              guidanceScale: watch("guidanceScale"),
              steps: watch("steps"),
              seed: watch("seed"),
              ...toolConfiguration
            } as unknown as Record<string, unknown>}
          />
        </div>
      </ToolLayout>
    </motion.div>
  );
}























