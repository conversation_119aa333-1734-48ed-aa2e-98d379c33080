import { useSelector, useDispatch } from "react-redux"
import type { RootState } from "@/types/store.types"
import { Sidebar } from "../../components/sidebar"
import { Header } from "@/components/header/Header"
import { APP_CONFIG } from "@/lib/config/app-config"
import { setActiveApp } from "@/lib/store/appSlice"
import { useEffect } from "react"

export function Dashboard() {
  const dispatch = useDispatch()
  const { activeApp, activeTabId, tabs } = useSelector((state: RootState) => state.app)

  useEffect(() => {
    if (!activeApp) {
      dispatch(setActiveApp("discover"))
    }
  }, [activeApp, dispatch])

  const renderContent = () => {
    if (!activeApp) return null

    const appConfig = APP_CONFIG[activeApp]
    if (!appConfig) return null

    // For tabbed applications
    if (appConfig.showTabs) {
      if (tabs.length === 0 || !activeTabId) {
        return (
          <div className="flex-1 flex items-center justify-center text-[#717680]">
            Select a tool from the sidebar to get started
          </div>
        )
      }

      const activeTab = tabs.find(tab => tab.id === activeTabId)
      if (!activeTab) return null
      const TabComponent = APP_CONFIG[activeTab.type]?.component
      return TabComponent ? <TabComponent key={activeTab.id} /> : null
    }

    // For non-tabbed applications
    const AppComponent = appConfig.component
    return AppComponent ? <AppComponent /> : null
  }

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header showTabs={APP_CONFIG[activeApp]?.showTabs} />
        {renderContent()}
      </div>
    </div>
  )
}








