import { BrandIdentitySection } from "./sections/BrandIdentitySection/BrandIdentitySection";
import { CategoriesSection } from "./sections/CategoriesSection/CategoriesSection";
import { EntrepreneurSection } from "./sections/EntrepreneurSection/EntrepreneurSection";
import { FooterLinksSection } from "./sections/FooterLinksSection/FooterLinksSection";
import { HeroSection } from "./sections/HeroSection/HeroSection";
import { MainContentSection } from "./sections/MainContentSection/MainContentSection";
import { PrivacySection } from "./sections/PrivacySection/PrivacySection";
import { ToolsSection } from "./sections/ToolsSection/ToolsSection";
import { WaitlistSection } from "./sections/WaitlistSection/WaitlistSection";
import { useSelector } from "react-redux";
import { Navigate } from "react-router-dom";
import { RootState } from "@/types/store.types";

const Landing = () => {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  // Redirect to dashboard if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <main className="flex flex-col w-full bg-white shadow-sm">
      <MainContentSection />
      <HeroSection />
      <ToolsSection />
      <BrandIdentitySection />
      <EntrepreneurSection />
      <CategoriesSection />
      <PrivacySection />
      <WaitlistSection />
      <FooterLinksSection />
    </main>
  );
};

export default Landing;
